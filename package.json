{"name": "olugo-standalone-app", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@tauri-apps/api": "^2", "@tauri-apps/plugin-opener": "^2", "node-pty": "^1.0.0", "react": "^18.3.1", "react-dom": "^18.3.1", "tauri-pty": "^0.1.0", "xterm": "^5.3.0", "xterm-addon-fit": "^0.8.0", "xterm-addon-web-links": "^0.9.0"}, "devDependencies": {"@tauri-apps/cli": "^2", "@types/node": "^22.15.19", "@types/react": "^18.3.21", "@types/react-dom": "^18.3.7", "@vitejs/plugin-react": "^4.3.4", "typescript": "~5.6.2", "vite": "^6.0.3"}}