const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipc<PERSON>enderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  loadApiKeys: () => ipcRenderer.invoke('load-api-keys'),
  saveApiKeys: (keys) => ipcRenderer.invoke('save-api-keys', keys),
  getOllamaModels: () => ipcRenderer.invoke('get-ollama-models'),
  sendChatMessage: (params) => ipcRenderer.invoke('send-chat-message', params),
  // We will add more exposed functions here as we migrate them
});

console.log('Preload script loaded.');
