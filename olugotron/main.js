const { app, B<PERSON>erWindow, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs').promises; // Use promises version of fs
const isDev = require('electron-is-dev');

const SETTINGS_FILE_NAME = 'settings.json';

async function getSettingsPath() {
  const userDataPath = app.getPath('userData');
  return path.join(userDataPath, SETTINGS_FILE_NAME);
}

async function saveApiKeys(event, keys) {
  try {
    const settingsPath = await getSettingsPath();
    const parentDir = path.dirname(settingsPath);
    await fs.mkdir(parentDir, { recursive: true });
    await fs.writeFile(settingsPath, JSON.stringify(keys, null, 2));
    return { success: true };
  } catch (error) {
    console.error('Failed to save API keys:', error);
    return { success: false, error: error.message };
  }
}

async function loadApiKeys() {
  try {
    const settingsPath = await getSettingsPath();
    try {
      const data = await fs.readFile(settingsPath, 'utf8');
      return JSON.parse(data);
    } catch (readError) {
      if (readError.code === 'ENOENT') {
        // File doesn't exist, return default (empty object or specific defaults)
        return {}; // Or some default ApiKeys structure
      }
      throw readError; // Re-throw other read errors
    }
  } catch (error) {
    console.error('Failed to load API keys:', error);
    return { error: error.message }; // Indicate error to renderer
  }
}

const DEFAULT_OLLAMA_URL = "http://localhost:11434";

async function getOllamaModels() {
  try {
    const apiKeys = await loadApiKeys(); // Re-use the existing function
    const baseUrl = apiKeys.ollama_url || DEFAULT_OLLAMA_URL;
    const url = `${baseUrl}/api/tags`;

    const response = await fetch(url);
    if (!response.ok) {
      const errorBody = await response.text().catch(() => "Could not get response text");
      throw new Error(`Ollama API request to ${url} failed with status: ${response.status}. Response: ${errorBody}`);
    }
    const data = await response.json();
    if (data && Array.isArray(data.models)) {
      return data.models.map(model => model.name);
    }
    return [];
  } catch (error) {
    console.error('Failed to get Ollama models:', error);
    return { error: error.message }; // Indicate error to renderer
  }
}

async function sendChatMessage(event, { provider, modelName, promptText, messages, imageDataUrl }) {
  // Note: promptText for Ollama is expected to be a JSON string of Vec<OllamaMessage>
  // imageDataUrl is for non-Ollama or simple Ollama user messages with an image.
  try {
    const apiKeys = await loadApiKeys();

    if (provider === 'Ollama') {
      const baseUrl = apiKeys.ollama_url || DEFAULT_OLLAMA_URL;
      const url = `${baseUrl}/api/chat`;
      let messagesToSend;

      try {
        // Attempt to parse promptText as a messages array (preferred for Ollama)
        messagesToSend = JSON.parse(promptText);
      } catch (e) {
        // If parsing fails, assume promptText is a simple string for a single user message
        let imagesBase64 = null;
        if (imageDataUrl) {
          const base64Part = imageDataUrl.split(',')[1];
          if (base64Part) {
            imagesBase64 = [base64Part];
          }
        }
        messagesToSend = [{ role: 'user', content: promptText, images: imagesBase64 }];
      }
      
      const requestPayload = {
        model: modelName,
        messages: messagesToSend,
        stream: false,
      };

      const response = await fetch(url, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestPayload),
      });

      if (!response.ok) {
        const errorBody = await response.text().catch(() => "Could not get response text");
        throw new Error(`Ollama API /api/chat request to ${url} failed with status: ${response.status}. Response: ${errorBody}`);
      }
      const data = await response.json();
      if (data.error) {
        throw new Error(`Ollama API error: ${data.error}`);
      }
      if (data.message && data.message.content) {
        return data.message.content;
      }
      throw new Error("Ollama /api/chat response missing 'message' object or content.");

    } else if (provider === 'LM Studio') {
      // TODO: Implement LM Studio
      console.warn('LM Studio provider not yet fully implemented in Electron main process.');
      return { error: 'LM Studio provider not yet implemented.' };
    } else if (provider === 'OpenRouter') {
      // TODO: Implement OpenRouter
      console.warn('OpenRouter provider not yet fully implemented in Electron main process.');
      return { error: 'OpenRouter provider not yet implemented.' };
    } else if (provider === 'Google Gemini') {
      // TODO: Implement Google Gemini
      console.warn('Google Gemini provider not yet fully implemented in Electron main process.');
      return { error: 'Google Gemini provider not yet implemented.' };
    } else {
      throw new Error(`Unsupported provider: ${provider}`);
    }
  } catch (error) {
    console.error(`Failed to send chat message for provider ${provider}:`, error);
    return { error: error.message }; // Indicate error to renderer
  }
}

function createWindow () {
  // Create the browser window.
  const mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'), // We'll create this later for IPC
      contextIsolation: true,
      nodeIntegration: false,
    }
  });

  // Load the Next.js app.
  if (isDev) {
    // In development, load the Next.js development server.
    // Make sure your Next.js app (webview-ui) is running on port 3000.
    mainWindow.loadURL('http://localhost:3000');
    // Open the DevTools.
    mainWindow.webContents.openDevTools();
  } else {
    // In production, load the static build of the Next.js app.
    // The 'webview-ui/out' directory should be copied to the resources path of the Electron app.
    // We'll configure electron-builder to do this.
    mainWindow.loadFile(path.join(__dirname, '../webview-ui/out/index.html'));
  }
}

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(async () => {
  // Setup IPC Handlers
  ipcMain.handle('load-api-keys', loadApiKeys);
  ipcMain.handle('save-api-keys', saveApiKeys);
  ipcMain.handle('get-ollama-models', getOllamaModels);
  ipcMain.handle('send-chat-message', sendChatMessage);

  createWindow();

  app.on('activate', function () {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', function () {
  if (process.platform !== 'darwin') app.quit();
});

// In this file you can include the rest of your app's specific main process
// code. You can also put them in separate files and require them here.
