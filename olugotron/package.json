{"name": "olugotron", "version": "1.0.0", "main": "main.js", "scripts": {"start": "electron .", "build": "electron-builder", "test": "echo \"Error: no test specified\" && exit 1"}, "build": {"appId": "com.example.olugotron", "productName": "Olugotron", "directories": {"output": "dist"}, "files": ["main.js", "preload.js", "package.json"], "mac": {"target": "dmg"}, "extraResources": [{"from": "../webview-ui/out", "to": "webview-ui/out", "filter": ["**/*"]}]}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"electron": "^36.2.1", "electron-builder": "^26.0.12"}, "dependencies": {"electron-is-dev": "^3.0.1"}}