[package]
name = "olugo-standalone-app"
version = "0.1.0"
description = "A Tauri App"
authors = ["Jeff Lugo"]
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
# The `_lib` suffix may seem redundant but it is necessary
# to make the lib name unique and wouldn't conflict with the bin name.
# This seems to be only an issue on Windows, see https://github.com/rust-lang/cargo/issues/8519
name = "olugo_standalone_app_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2", features = [] }

[dependencies]
tauri = { version = "2", features = ["macos-private-api"] }
tauri-plugin-opener = "2"
serde = { version = "1", features = ["derive"] }
serde_json = "1"
reqwest = { version = "0.12", features = ["json"] }
tauri-plugin-dialog = "2.0.0-beta.8"
tauri-plugin-fs = "2.0.0-beta.8"
base64 = "0.22"
tokio = { version = "1", features = ["sync", "time", "rt-multi-thread", "macros"] }
walkdir = "2"
uuid = { version = "1", features = ["v4", "serde"] } # Added uuid
portable-pty = "0.8.1"
libc = "0.2"
# tauri-plugin-pty = "0.1.0" # Removed as new terminal uses portable-pty directly
