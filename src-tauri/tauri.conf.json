{"$schema": "https://schema.tauri.app/config/2", "productName": "olugo-standalone-app", "version": "0.1.0", "identifier": "com.olugo-standalone-app.app", "build": {"beforeDevCommand": "cd webview-ui && npm run dev", "devUrl": "http://localhost:3000", "beforeBuildCommand": "cd webview-ui && npm run build", "frontendDist": "../webview-ui/out"}, "app": {"windows": [{"title": "OLugo AI Assistant", "width": 1024, "height": 768, "transparent": false}], "security": {"csp": null}, "macOSPrivateApi": true, "withGlobalTauri": true}, "plugins": {"shell": {"open": true}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}