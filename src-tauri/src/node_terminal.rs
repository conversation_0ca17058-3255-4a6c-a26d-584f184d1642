use tauri::{AppHandle, Runtime, Emitter};

/// Create a new terminal process using node-pty
#[tauri::command]
pub async fn create_node_terminal<R: Runtime>(app: AppHandle<R>) -> Result<serde_json::Value, String> {
    // Call the Node.js function to create a terminal
    app.emit("node-terminal-create", ())
        .map_err(|e| format!("Failed to emit terminal creation event: {}", e))?;

    // Return a placeholder terminal ID
    // The actual ID will be sent back via an event from the Node.js side
    Ok(serde_json::json!({
        "terminalId": "pending"
    }))
}

/// Write data to a terminal
#[tauri::command]
pub async fn write_to_node_terminal<R: Runtime>(
    app: AppHandle<R>,
    terminal_id: String,
    data: String,
) -> Result<(), String> {
    // Create the payload for the event
    let payload = serde_json::json!({
        "terminalId": terminal_id,
        "data": data
    });

    // Emit the event to the Node.js side
    app.emit("node-terminal-write", payload)
        .map_err(|e| format!("Failed to emit terminal write event: {}", e))?;

    Ok(())
}

/// Resize a terminal
#[tauri::command]
pub async fn resize_node_terminal<R: Runtime>(
    app: AppHandle<R>,
    terminal_id: String,
    cols: u16,
    rows: u16,
) -> Result<(), String> {
    // Create the payload for the event
    let payload = serde_json::json!({
        "terminalId": terminal_id,
        "cols": cols,
        "rows": rows
    });

    // Emit the event to the Node.js side
    app.emit("node-terminal-resize", payload)
        .map_err(|e| format!("Failed to emit terminal resize event: {}", e))?;

    Ok(())
}

/// Kill a terminal
#[tauri::command]
pub async fn kill_node_terminal<R: Runtime>(
    app: AppHandle<R>,
    terminal_id: String,
) -> Result<(), String> {
    // Create the payload for the event
    let payload = serde_json::json!({
        "terminalId": terminal_id
    });

    // Emit the event to the Node.js side
    app.emit("node-terminal-kill", payload)
        .map_err(|e| format!("Failed to emit terminal kill event: {}", e))?;

    Ok(())
}

/// List all terminals
#[tauri::command]
pub async fn list_node_terminals<R: Runtime>(app: AppHandle<R>) -> Result<serde_json::Value, String> {
    // Emit the event to the Node.js side
    app.emit("node-terminal-list", ())
        .map_err(|e| format!("Failed to emit terminal list event: {}", e))?;

    // Return a placeholder response
    // The actual list will be sent back via an event from the Node.js side
    Ok(serde_json::json!([]))
}
