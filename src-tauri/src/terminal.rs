use portable_pty::{native_pty_system, CommandBuilder, PtyPair, PtySize};
use std::{
    io::{BufReader, Read, Write},
    sync::Arc,
    thread,
};
use tauri::{async_runtime::Mutex as AsyncMutex, State};
use tokio::time::{sleep, Duration};

/// State structure to hold the PTY pair and I/<PERSON> handles
pub struct TerminalState {
    pty_pair: Arc<AsyncMutex<Option<PtyPair>>>,
    writer: Arc<AsyncMutex<Option<Box<dyn Write + Send>>>>,
    reader: Arc<AsyncMutex<Option<BufReader<Box<dyn Read + Send>>>>>,
    is_shell_running: Arc<AsyncMutex<bool>>,
}

/// Create a new terminal state
pub fn create_terminal_state() -> TerminalState {
    TerminalState {
        pty_pair: Arc::new(AsyncMutex::new(None)),
        writer: Arc::new(AsyncMutex::new(None)),
        reader: Arc::new(AsyncMutex::new(None)),
        is_shell_running: Arc::new(AsyncMutex::new(false)),
    }
}

/// Initialize the PTY
async fn initialize_pty(state: &TerminalState) -> Result<(), String> {
    let mut pty_pair_lock = state.pty_pair.lock().await;
    let mut writer_lock = state.writer.lock().await;
    let mut reader_lock = state.reader.lock().await;

    // Check if we need to reinitialize due to errors
    let needs_reinit = if let Some(_) = &*pty_pair_lock {
        // Try to check if the PTY is still valid
        if writer_lock.is_none() || reader_lock.is_none() {
            println!("PTY needs reinitialization: writer or reader is missing");
            true
        } else {
            // Try to check if the reader is still valid by attempting a non-blocking read
            if let Some(reader) = reader_lock.as_mut() {
                let mut buf = [0u8; 1];
                match reader.read(&mut buf) {
                    Ok(_) => false, // Read succeeded or would block, PTY is valid
                    Err(e) if e.kind() == std::io::ErrorKind::WouldBlock => false, // This is expected for non-blocking read
                    Err(e) => {
                        println!("PTY needs reinitialization: reader error: {}", e);
                        true
                    }
                }
            } else {
                true
            }
        }
    } else {
        // No PTY pair, need to initialize
        true
    };

    // Initialize or reinitialize if needed
    if needs_reinit {
        println!("Initializing PTY");

        // Clear existing PTY if any
        *pty_pair_lock = None;
        *writer_lock = None;
        *reader_lock = None;

        // Create a new PTY with a larger size
        let pty_system = native_pty_system();
        let pty_pair = pty_system
            .openpty(PtySize {
                rows: 24,
                cols: 80,
                pixel_width: 0,
                pixel_height: 0,
            })
            .map_err(|err| {
                let err_msg = format!("Failed to open PTY: {}", err);
                eprintln!("{}", err_msg);
                err_msg
            })?;

        // Get reader and writer with better error handling
        let reader = match pty_pair.master.try_clone_reader() {
            Ok(r) => r,
            Err(err) => {
                let err_msg = format!("Failed to clone PTY reader: {}", err);
                eprintln!("{}", err_msg);
                return Err(err_msg);
            }
        };

        let writer = match pty_pair.master.take_writer() {
            Ok(w) => w,
            Err(err) => {
                let err_msg = format!("Failed to take PTY writer: {}", err);
                eprintln!("{}", err_msg);
                return Err(err_msg);
            }
        };

        // Store the PTY components
        *pty_pair_lock = Some(pty_pair);
        *writer_lock = Some(writer);
        *reader_lock = Some(BufReader::new(reader));

        println!("PTY initialized successfully");
    }

    Ok(())
}

/// Create a shell process in the PTY
#[tauri::command]
pub async fn create_terminal_shell(state: State<'_, TerminalState>) -> Result<(), String> {
    // Check if shell is already running
    {
        let is_running = state.is_shell_running.lock().await;
        if *is_running {
            println!("Shell is already running, not creating a new one");
            return Ok(());
        }
    }

    println!("Creating new shell");

    // Initialize PTY if needed
    initialize_pty(&state).await?;

    // Determine which shell to use based on the platform
    #[cfg(target_os = "windows")]
    let mut cmd = CommandBuilder::new("powershell.exe");
    #[cfg(target_os = "macos")]
    let mut cmd = CommandBuilder::new("/bin/zsh");
    #[cfg(not(any(target_os = "windows", target_os = "macos")))]
    let mut cmd = CommandBuilder::new("bash");

    // Add the $TERM env variable so we can use clear and other commands
    #[cfg(target_os = "windows")]
    cmd.env("TERM", "cygwin");
    #[cfg(not(target_os = "windows"))]
    cmd.env("TERM", "xterm-256color");

    // Set login shell to ensure proper initialization
    #[cfg(not(target_os = "windows"))]
    cmd.args(&["-l"]);

    // Set environment variables for better terminal experience
    cmd.env("COLORTERM", "truecolor");
    cmd.env("TERM_PROGRAM", "olugo-terminal");

    // Get the current directory
    let current_dir = std::env::current_dir().map_err(|e| e.to_string())?;
    cmd.cwd(&current_dir);

    // Get the PTY pair
    let pty_pair_lock = state.pty_pair.lock().await;
    let pty_pair = pty_pair_lock.as_ref().ok_or_else(|| "PTY not initialized".to_string())?;

    // Spawn the command
    let mut child = match pty_pair.slave.spawn_command(cmd) {
        Ok(child) => child,
        Err(err) => {
            let err_msg = format!("Failed to spawn shell: {}", err);
            eprintln!("{}", err_msg);

            // Try with a different shell as fallback
            #[cfg(target_os = "macos")]
            {
                println!("Trying fallback to /bin/bash");
                let mut cmd = CommandBuilder::new("/bin/bash");
                cmd.env("TERM", "xterm-256color");
                cmd.args(&["-l"]);
                cmd.env("COLORTERM", "truecolor");
                cmd.env("TERM_PROGRAM", "olugo-terminal");
                cmd.cwd(&current_dir);

                match pty_pair.slave.spawn_command(cmd) {
                    Ok(child) => child,
                    Err(err) => {
                        let err_msg = format!("Failed to spawn fallback shell: {}", err);
                        eprintln!("{}", err_msg);
                        return Err(err_msg);
                    }
                }
            }
            #[cfg(not(target_os = "macos"))]
            {
                return Err(err_msg);
            }
        }
    };

    println!("Shell process started");

    // Mark shell as running
    {
        let mut is_running = state.is_shell_running.lock().await;
        *is_running = true;
    }

    // Spawn a thread to wait for the child process to exit
    let is_shell_running = state.is_shell_running.clone();
    thread::spawn(move || {
        let status = match child.wait() {
            Ok(status) => status,
            Err(e) => {
                eprintln!("Error waiting for child process: {}", e);
                return;
            }
        };

        // Mark shell as not running
        tokio::runtime::Handle::current().block_on(async {
            let mut is_running = is_shell_running.lock().await;
            *is_running = false;
        });

        println!("Shell process exited with code: {}", status.exit_code());
    });

    // Give the shell a moment to start
    sleep(Duration::from_millis(500)).await;

    // Send an initial command to ensure the shell is ready
    {
        let mut writer_lock = state.writer.lock().await;
        if let Some(writer) = writer_lock.as_mut() {
            // Send a newline to get the prompt
            if let Err(e) = writer.write_all(b"\r\n") {
                eprintln!("Error sending initial command: {}", e);
            } else if let Err(e) = writer.flush() {
                eprintln!("Error flushing writer: {}", e);
            }
        }
    }

    println!("Shell initialized and ready");

    Ok(())
}

/// Write data to the PTY
#[tauri::command]
pub async fn write_to_terminal(data: String, state: State<'_, TerminalState>) -> Result<(), String> {
    println!("Writing to terminal: {:?}", data);

    // Try to write to the terminal, with up to 2 retries on permission errors
    let mut retries = 0;
    let max_retries = 2;

    while retries <= max_retries {
        // Initialize PTY if needed
        initialize_pty(&state).await?;

        // Check if shell is running
        {
            let is_running = state.is_shell_running.lock().await;
            if !*is_running {
                println!("Shell is not running, starting a new one");
                // Drop the lock before calling create_terminal_shell
                drop(is_running);
                match create_terminal_shell(state.clone()).await {
                    Ok(_) => {
                        // Give the shell a moment to start
                        sleep(Duration::from_millis(100)).await;
                    },
                    Err(e) => {
                        let err_msg = format!("Failed to create shell: {}", e);
                        eprintln!("{}", err_msg);
                        return Err(err_msg);
                    }
                }
            }
        }

        // Get the writer
        let mut writer_lock = state.writer.lock().await;
        let writer = match writer_lock.as_mut() {
            Some(w) => w,
            None => {
                drop(writer_lock);
                if retries < max_retries {
                    println!("Writer not initialized, recreating shell (retry {}/{})", retries + 1, max_retries);
                    // Mark shell as not running so it will be recreated
                    let mut is_running = state.is_shell_running.lock().await;
                    *is_running = false;
                    drop(is_running);
                    retries += 1;
                    continue;
                } else {
                    return Err("Writer not initialized after multiple attempts".to_string());
                }
            }
        };

        // Write the data
        let write_result = writer.write_all(data.as_bytes());
        if let Err(e) = write_result {
            // Handle permission errors gracefully
            if e.kind() == std::io::ErrorKind::PermissionDenied && retries < max_retries {
                println!("Permission denied when writing to terminal, recreating shell (retry {}/{})", retries + 1, max_retries);
                drop(writer_lock); // Release the lock before recreating the shell

                // Mark shell as not running so it will be recreated
                let mut is_running = state.is_shell_running.lock().await;
                *is_running = false;
                drop(is_running);

                // Try to recreate the shell
                match create_terminal_shell(state.clone()).await {
                    Ok(_) => {
                        println!("Successfully recreated shell after permission error");
                        // Try writing again after a short delay
                        sleep(Duration::from_millis(100)).await;
                        retries += 1;
                        continue;
                    },
                    Err(e) => {
                        let err_msg = format!("Failed to recreate shell after permission error: {}", e);
                        eprintln!("{}", err_msg);
                        return Err(err_msg);
                    }
                }
            } else {
                let err_msg = format!("Error writing to terminal: {}", e);
                eprintln!("{}", err_msg);
                return Err(err_msg);
            }
        }

        // Flush the writer to ensure the data is sent immediately
        let flush_result = writer.flush();
        if let Err(e) = flush_result {
            // Handle permission errors gracefully
            if e.kind() == std::io::ErrorKind::PermissionDenied && retries < max_retries {
                println!("Permission denied when flushing terminal writer, recreating shell (retry {}/{})", retries + 1, max_retries);
                drop(writer_lock); // Release the lock before recreating the shell

                // Mark shell as not running so it will be recreated
                let mut is_running = state.is_shell_running.lock().await;
                *is_running = false;
                drop(is_running);

                // Try to recreate the shell
                match create_terminal_shell(state.clone()).await {
                    Ok(_) => {
                        println!("Successfully recreated shell after permission error");
                        // Try writing again after a short delay
                        sleep(Duration::from_millis(100)).await;
                        retries += 1;
                        continue;
                    },
                    Err(e) => {
                        let err_msg = format!("Failed to recreate shell after permission error: {}", e);
                        eprintln!("{}", err_msg);
                        return Err(err_msg);
                    }
                }
            } else {
                let err_msg = format!("Error flushing terminal writer: {}", e);
                eprintln!("{}", err_msg);
                return Err(err_msg);
            }
        }

        // If we got here, the write was successful
        println!("Successfully wrote data to terminal");
        return Ok(());
    }

    // If we got here, we've exceeded our retry limit
    Err("Failed to write to terminal after multiple attempts".to_string())
}

/// Read data from the PTY
#[tauri::command]
pub async fn read_from_terminal(state: State<'_, TerminalState>) -> Result<Option<String>, String> {
    // Initialize PTY if needed
    initialize_pty(&state).await?;

    // Check if shell is running
    {
        let is_running = state.is_shell_running.lock().await;
        if !*is_running {
            println!("Shell is not running when trying to read, starting a new one");
            // Drop the lock before calling create_terminal_shell
            drop(is_running);
            create_terminal_shell(state.clone()).await?;

            // Give the shell a moment to start and produce output
            sleep(Duration::from_millis(100)).await;
        }
    }

    // Get the reader
    let mut reader_lock = state.reader.lock().await;
    let reader = reader_lock.as_mut().ok_or_else(|| "Reader not initialized".to_string())?;

    // Create a buffer to read into
    let mut buffer = [0u8; 4096];

    // Read with a timeout
    let read_timeout = Duration::from_millis(50);

    // Use a simpler approach with a timeout
    let read_result = tokio::time::timeout(read_timeout, async {
        match reader.read(&mut buffer) {
            Ok(n) => Ok(n),
            Err(e) => {
                if e.kind() == std::io::ErrorKind::PermissionDenied {
                    println!("Permission denied when reading from terminal");
                    Err("permission_denied".to_string())
                } else {
                    let err_msg = format!("Error reading from terminal: {}", e);
                    eprintln!("{}", err_msg);
                    Err(err_msg)
                }
            }
        }
    }).await;

    // Handle timeout
    let read_result = match read_result {
        Ok(result) => result, // Got a result within the timeout
        Err(_) => Ok(0),      // Timeout occurred, return 0 bytes read
    };

    // Handle the result
    match read_result {
        Ok(bytes_read) => {
            // Convert the read bytes to a string if we got any
            if bytes_read > 0 {
                match std::str::from_utf8(&buffer[0..bytes_read]) {
                    Ok(s) => {
                        let result = Some(s.to_string());
                        println!("Read from terminal: {:?}", result);
                        Ok(result)
                    },
                    Err(e) => {
                        let err_msg = format!("Error converting terminal output to UTF-8: {}", e);
                        eprintln!("{}", err_msg);
                        Err(err_msg)
                    }
                }
            } else {
                // No data available
                Ok(None)
            }
        },
        Err(e) => {
            if e == "permission_denied".to_string() {
                // Handle permission errors by recreating the shell
                drop(reader_lock);

                // Mark shell as not running so it will be recreated
                let mut is_running = state.is_shell_running.lock().await;
                *is_running = false;
                drop(is_running);

                // Try to recreate the shell
                match create_terminal_shell(state.clone()).await {
                    Ok(_) => {
                        println!("Successfully recreated shell after permission error");
                        Ok(Some("Shell restarted due to permission error. Please try your command again.\r\n".to_string()))
                    },
                    Err(e) => {
                        let err_msg = format!("Failed to recreate shell after permission error: {}", e);
                        eprintln!("{}", err_msg);
                        Err(err_msg)
                    }
                }
            } else {
                Err(e.to_string())
            }
        }
    }
}

/// Resize the PTY
#[tauri::command]
pub async fn resize_terminal(rows: u16, cols: u16, state: State<'_, TerminalState>) -> Result<(), String> {
    // Initialize PTY if needed
    initialize_pty(&state).await?;

    // Get the PTY pair
    let pty_pair_lock = state.pty_pair.lock().await;
    let pty_pair = pty_pair_lock.as_ref().ok_or_else(|| "PTY not initialized".to_string())?;

    // Resize the terminal
    pty_pair
        .master
        .resize(PtySize {
            rows,
            cols,
            pixel_width: 0,
            pixel_height: 0,
        })
        .map_err(|e| e.to_string())
}
