use portable_pty::{native_pty_system, CommandBuilder, PtyPair, PtySize}; // Removed MasterPty, SlavePty
use std::{
    io::{<PERSON>ufReader, Read, Write},
    sync::Arc,
    // thread, // Removed unused import
    collections::HashMap,
};
use tauri::{async_runtime::Mutex as AsyncMutex, AppHandle, Emitter, State, Window}; // Removed Manager
use tokio::sync::mpsc::{self, UnboundedSender}; // For sending commands to the shell task

// Unique ID for each terminal instance
type TerminalId = String;

// State for a single PTY instance
struct PtyInstance {
    pty_pair: PtyPair,
    writer: Box<dyn Write + Send>,
    // Reader is handled by a dedicated task
    shell_command_tx: UnboundedSender<ShellCommand>, // Channel to send commands to the shell task
    is_running: bool,
}

// Commands that can be sent to the shell task
enum ShellCommand {
    Write(String), // Data to write to the PTY
    Resize(PtySize),
    Shutdown,
}

// Main state for managing multiple PTY instances
pub struct YakuakeTerminalState {
    instances: Arc<AsyncMutex<HashMap<TerminalId, PtyInstance>>>,
    app_handle: AppHandle,
}

impl YakuakeTerminalState {
    pub fn new(app_handle: AppHandle) -> Self {
        Self {
            instances: Arc::new(AsyncMutex::new(HashMap::new())),
            app_handle,
        }
    }
}

fn generate_terminal_id() -> TerminalId {
    uuid::Uuid::new_v4().to_string()
}

// Tauri command to create a new PTY instance and spawn a shell
#[tauri::command]
pub async fn yk_create_terminal(
    state: State<'_, YakuakeTerminalState>,
    window: Window, // To emit events to a specific window
) -> Result<TerminalId, String> {
    let terminal_id = generate_terminal_id();
    println!("Creating Yakuake terminal with ID: {}", terminal_id);

    let pty_system = native_pty_system();
    let pty_pair = pty_system
        .openpty(PtySize {
            rows: 24,
            cols: 80,
            pixel_width: 0,
            pixel_height: 0,
        })
        .map_err(|e| format!("Failed to open PTY: {}", e))?;

    let writer = pty_pair.master.take_writer().map_err(|e| format!("Failed to take PTY writer: {}", e))?;
    let mut reader = BufReader::new(pty_pair.master.try_clone_reader().map_err(|e| format!("Failed to clone PTY reader: {}", e))?);

    // Determine shell based on platform
    #[cfg(target_os = "windows")]
    let shell_program = "powershell.exe";
    #[cfg(target_os = "macos")]
    let shell_program = "/bin/zsh"; // Default to zsh on macOS
    #[cfg(not(any(target_os = "windows", target_os = "macos")))]
    let shell_program = "bash"; // Default to bash on Linux/other

    let mut cmd = CommandBuilder::new(shell_program);
    // Set TERM for compatibility
    #[cfg(target_os = "windows")]
    cmd.env("TERM", "cygwin");
    #[cfg(not(target_os = "windows"))]
    cmd.env("TERM", "xterm-256color");
    cmd.env("COLORTERM", "truecolor");
    cmd.env("TERM_PROGRAM", "lugari-yakuake");

    // Set login shell argument for non-Windows to ensure profile sourcing
    #[cfg(not(target_os = "windows"))]
    {
        if shell_program == "/bin/zsh" || shell_program == "/bin/bash" {
            cmd.arg("-l");
        }
    }

    let current_dir = std::env::current_dir().map_err(|e| format!("Failed to get current directory: {}", e))?;
    cmd.cwd(current_dir);

    let mut child = pty_pair.slave.spawn_command(cmd)
        .map_err(|e| format!("Failed to spawn shell '{}': {}", shell_program, e))?;

    println!("Shell process spawned for terminal ID: {}", terminal_id);

    let (shell_command_tx, mut shell_command_rx) = mpsc::unbounded_channel::<ShellCommand>();

    let instance = PtyInstance {
        pty_pair,
        writer,
        shell_command_tx,
        is_running: true,
    };

    state.instances.lock().await.insert(terminal_id.clone(), instance);

    let app_handle_clone = state.app_handle.clone();
    let terminal_id_clone_reader = terminal_id.clone();
    let window_clone = window.clone();

    // Task for reading PTY output and emitting to frontend
    tokio::spawn(async move {
        let mut buffer = [0u8; 4096];
        loop {
            match reader.read(&mut buffer) {
                Ok(0) => { // EOF, PTY closed
                    println!("PTY reader EOF for terminal ID: {}", terminal_id_clone_reader);
                    let _ = window_clone.emit(&format!("yk-terminal-data-{}", terminal_id_clone_reader), "\r\nShell exited.\r\n");
                    let _ = window_clone.emit(&format!("yk-terminal-closed-{}", terminal_id_clone_reader), ());
                    break;
                }
                Ok(n) => {
                    if let Ok(output_str) = String::from_utf8(buffer[..n].to_vec()) {
                        // println!("PTY data for {}: {:?}", terminal_id_clone_reader, output_str);
                        if let Err(e) = window_clone.emit(&format!("yk-terminal-data-{}", terminal_id_clone_reader), output_str) {
                            eprintln!("Failed to emit terminal data for {}: {}", terminal_id_clone_reader, e);
                        }
                    } else {
                         eprintln!("Received non-UTF8 data from PTY for terminal ID: {}", terminal_id_clone_reader);
                    }
                }
                Err(e) => {
                    eprintln!("Error reading from PTY for terminal ID {}: {}", terminal_id_clone_reader, e);
                    let _ = window_clone.emit(&format!("yk-terminal-error-{}", terminal_id_clone_reader), format!("PTY read error: {}", e));
                    break;
                }
            }
        }
        println!("PTY reader task finished for terminal ID: {}", terminal_id_clone_reader);
    });

    let instances_clone = state.instances.clone();
    let terminal_id_clone_manager = terminal_id.clone();
    let app_handle_manager_clone = state.app_handle.clone();
    let window_manager_clone = window.clone();

    // Task for managing the shell process and handling commands
    tokio::spawn(async move {
        let child_id = child.process_id();
        println!("Shell manager task started for PID: {:?}, Terminal ID: {}", child_id, terminal_id_clone_manager);

        loop {
            // Check if the shell is still supposed to be running before selecting
            let is_expected_to_be_running = {
                let guard = instances_clone.lock().await;
                guard.get(&terminal_id_clone_manager).map_or(false, |inst| inst.is_running)
            };

            if !is_expected_to_be_running {
                // If a shutdown was commanded and kill was attempted, or instance removed.
                // Poll one last time to confirm exit, then break.
                match child.try_wait() {
                    Ok(Some(status)) => {
                        println!("Shell process for terminal {} (expected shutdown) exited with status: {}", terminal_id_clone_manager, status);
                    }
                    Ok(None) => {
                         println!("Shell process for terminal {} (expected shutdown) still running, attempting kill again.", terminal_id_clone_manager);
                         if let Err(e) = child.kill() {
                             eprintln!("Failed to kill child (during shutdown poll) for terminal {}: {}", terminal_id_clone_manager, e);
                         }
                         // Give a moment for kill to take effect
                         tokio::time::sleep(std::time::Duration::from_millis(50)).await;
                         if let Ok(Some(status_after_kill)) = child.try_wait() {
                            println!("Shell process for terminal {} (after kill attempt) exited with status: {}", terminal_id_clone_manager, status_after_kill);
                         } else {
                            println!("Shell process for terminal {} still running after kill attempt.", terminal_id_clone_manager);
                         }
                    }
                    Err(e) => {
                        eprintln!("Error checking shell status (expected shutdown) for terminal {}: {}", terminal_id_clone_manager, e);
                    }
                }
                let _ = window_manager_clone.emit(&format!("yk-terminal-closed-{}", terminal_id_clone_manager), ());
                break; // Exit manager loop
            }

            tokio::select! {
                biased; // Process commands first

                shell_cmd_opt = shell_command_rx.recv() => {
                    if let Some(cmd) = shell_cmd_opt {
                        let mut instances_guard = instances_clone.lock().await;
                        if let Some(instance) = instances_guard.get_mut(&terminal_id_clone_manager) {
                            match cmd {
                                ShellCommand::Write(data) => {
                                    if instance.is_running {
                                        if let Err(e) = instance.writer.write_all(data.as_bytes()) {
                                            eprintln!("Error writing to PTY for {}: {}", terminal_id_clone_manager, e);
                                        }
                                        if let Err(e) = instance.writer.flush() {
                                           eprintln!("Error flushing PTY for {}: {}", terminal_id_clone_manager, e);
                                        }
                                    }
                                }
                                ShellCommand::Resize(size) => {
                                    if instance.is_running {
                                        if let Err(e) = instance.pty_pair.master.resize(size) {
                                            eprintln!("Error resizing PTY for {}: {}", terminal_id_clone_manager, e);
                                        }
                                    }
                                }
                                ShellCommand::Shutdown => {
                                    println!("Shutdown command received for terminal ID: {}", terminal_id_clone_manager);
                                    if instance.is_running {
                                        instance.is_running = false; // Mark as not running
                                        if let Err(e) = child.kill() {
                                            eprintln!("Failed to kill child process for terminal {}: {}", terminal_id_clone_manager, e);
                                        }
                                        // Loop will break due to is_expected_to_be_running check or try_wait
                                    }
                                }
                            }
                        } else {
                             println!("Instance {} not found during command processing, manager task stopping.", terminal_id_clone_manager);
                            break; // Instance gone
                        }
                    } else {
                        // Channel closed. If shell is running, it will be handled by try_wait.
                        println!("Shell command channel closed for {}. Instance will be cleaned up if shell exits.", terminal_id_clone_manager);
                        // No longer break here; let the is_expected_to_be_running and try_wait handle termination.
                        // If instance.is_running is still true, the loop continues to poll try_wait.
                        // If instance.is_running becomes false (e.g. by another mechanism), loop will break.
                    }
                }

                // Poll for child process exit using try_wait
                _ = tokio::time::sleep(std::time::Duration::from_millis(200)) => {
                    match child.try_wait() {
                        Ok(Some(status)) => { // Process has exited
                            println!("Shell process for terminal {} polled and found exited with status: {}", terminal_id_clone_manager, status);
                            if let Some(instance) = instances_clone.lock().await.get_mut(&terminal_id_clone_manager) {
                                instance.is_running = false;
                            }
                            // is_expected_to_be_running will be false on next iteration, leading to break
                        }
                        Ok(None) => {
                            // Process is still running, continue loop
                        }
                        Err(e) => { // Error trying to get status
                            eprintln!("Error polling shell status for terminal {}: {}", terminal_id_clone_manager, e);
                            if let Some(instance) = instances_clone.lock().await.get_mut(&terminal_id_clone_manager) {
                                instance.is_running = false; // Assume it's dead
                            }
                            // is_expected_to_be_running will be false on next iteration, leading to break
                        }
                    }
                }
            }
        }
        println!("Shell manager task finished for terminal ID: {}", terminal_id_clone_manager);
        // Remove instance from state after task finishes
        instances_clone.lock().await.remove(&terminal_id_clone_manager);
        // println!("Instance {} removed from state.", terminal_id_clone_manager); // This was part of the unreachable code logic
    });

    // Send initial newline to trigger prompt
    if let Some(instance) = state.instances.lock().await.get(&terminal_id) {
        if let Err(e) = instance.shell_command_tx.send(ShellCommand::Write("\r\n".to_string())) {
            eprintln!("Failed to send initial newline to terminal {}: {}", terminal_id, e);
        }
    }

    Ok(terminal_id)
}

// Tauri command to write to a PTY instance
#[tauri::command]
pub async fn yk_write_to_terminal(
    terminal_id: TerminalId,
    data: String,
    state: State<'_, YakuakeTerminalState>,
) -> Result<(), String> {
    // println!("yk_write_to_terminal called for {}: {:?}", terminal_id, data);
    let instances = state.instances.lock().await;
    if let Some(instance) = instances.get(&terminal_id) {
        if instance.is_running {
            instance.shell_command_tx.send(ShellCommand::Write(data))
                .map_err(|e| format!("Failed to send write command to terminal {}: {}", terminal_id, e))
        } else {
            Err(format!("Terminal {} is not running.", terminal_id))
        }
    } else {
        Err(format!("Terminal with ID {} not found.", terminal_id))
    }
}

// Tauri command to resize a PTY instance
#[tauri::command]
pub async fn yk_resize_terminal(
    terminal_id: TerminalId,
    rows: u16,
    cols: u16,
    state: State<'_, YakuakeTerminalState>,
) -> Result<(), String> {
    println!("yk_resize_terminal called for {}: rows={}, cols={}", terminal_id, rows, cols);
    let instances = state.instances.lock().await;
    if let Some(instance) = instances.get(&terminal_id) {
         if instance.is_running {
            let size = PtySize { rows, cols, pixel_width: 0, pixel_height: 0 };
            instance.shell_command_tx.send(ShellCommand::Resize(size))
                .map_err(|e| format!("Failed to send resize command to terminal {}: {}", terminal_id, e))
        } else {
            Err(format!("Terminal {} is not running.", terminal_id))
        }
    } else {
        Err(format!("Terminal with ID {} not found.", terminal_id))
    }
}

// Tauri command to close/kill a PTY instance
#[tauri::command]
pub async fn yk_close_terminal(
    terminal_id: TerminalId,
    state: State<'_, YakuakeTerminalState>,
) -> Result<(), String> {
    println!("yk_close_terminal called for {}", terminal_id);
    let instances = state.instances.lock().await; // Removed mut
    if let Some(instance) = instances.get(&terminal_id) {
        if instance.is_running {
             if let Err(e) = instance.shell_command_tx.send(ShellCommand::Shutdown) {
                eprintln!("Failed to send shutdown command to terminal {}: {}", terminal_id, e);
                // Proceed to remove even if send fails, as the task might already be stopping
             }
        }
        // The instance will be removed by its manager task upon exit.
        // For immediate feedback or if the task is stuck, we could remove here,
        // but it's cleaner to let the task handle its own cleanup.
        Ok(())
    } else {
        Err(format!("Terminal with ID {} not found for closing.", terminal_id))
    }
}

// New command to send a command string directly to the shell
#[tauri::command]
pub async fn yk_send_command_to_shell(
    terminal_id: TerminalId,
    command: String,
    state: State<'_, YakuakeTerminalState>,
) -> Result<(), String> {
    println!("yk_send_command_to_shell for {}: {}", terminal_id, command);
    let instances = state.instances.lock().await;
    if let Some(instance) = instances.get(&terminal_id) {
        if instance.is_running {
            // Ensure the command is newline-terminated for execution
            let mut full_command = command;
            if !full_command.ends_with('\n') && !full_command.ends_with('\r') {
                full_command.push('\r'); // Use \r for PTYs, often translates to newline
            }
            instance.shell_command_tx.send(ShellCommand::Write(full_command))
                .map_err(|e| format!("Failed to send command to terminal {}: {}", terminal_id, e))
        } else {
            Err(format!("Terminal {} is not running.", terminal_id))
        }
    } else {
        Err(format!("Terminal with ID {} not found for sending command.", terminal_id))
    }
}
