use base64::{engine::general_purpose::STANDARD as BASE64_STANDARD, Engine as _};
use serde::{Deserialize, Serialize};
use std::fs;
use std::io::Read;
use std::path::PathBuf;
use tauri::{Window, AppH<PERSON><PERSON>, Manager};
use tauri_plugin_dialog::{DialogExt, FilePath};
use tokio::sync::oneshot;
use walkdir::WalkDir; // Added for directory traversal
use std::path::Path; // Added for path manipulation

// Import modules
// mod terminal;
// mod node_terminal;
mod yakuake_terminal; // Added new terminal module

// --- Structs ---
#[derive(Deserialize, Debug)]
struct OllamaModel { name: String }
#[derive(Deserialize, Debug)]
struct OllamaTagsResponse { models: Vec<OllamaModel> }

#[derive(Deserialize, Debug)]
struct ModelIdentifier { id: String }

#[derive(Deserialize, Debug)]
struct LmStudioModelsResponse { data: Vec<ModelIdentifier> }

#[derive(Deserialize, Debug)]
struct OpenRouterModelsResponse {
    data: Vec<ModelIdentifier>,
}

#[derive(Deserialize, Debug)]
struct GeminiModel {
    name: String,
    display_name: Option<String>,
    description: Option<String>,
}
#[derive(Deserialize, Debug)]
struct GeminiModelsResponse {
    models: Vec<GeminiModel>,
}

// --- Chat Structs ---

// Ollama specific
#[derive(Deserialize, Debug, Serialize, Clone)] // Added Serialize, Clone
struct OllamaMessage {
    role: String,
    content: String,
    #[serde(skip_serializing_if = "Option::is_none")] // Ensure images field is omitted if None
    images: Option<Vec<String>>,
}

#[derive(Serialize, Debug)]
struct OllamaChatRequest {
    model: String,
    messages: Vec<OllamaMessage>, // Changed from prompt: String
    stream: bool,
    // images field is now part of OllamaMessage if needed by a specific message
}

#[derive(Deserialize, Debug)]
struct OllamaChatResponse {
    message: Option<OllamaMessage>, // This will contain the response message
    done: bool,
    error: Option<String>,
    // Removed 'response: Option<String>' as /api/chat returns a message object
}


// OpenAI/OpenRouter/LM Studio Compatible Chat Structs
#[derive(Serialize, Deserialize, Debug, Clone)]
struct OpenAiContentPart {
    #[serde(rename = "type")]
    part_type: String,
    text: Option<String>,
    image_url: Option<OpenAiImageUrl>,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
struct OpenAiImageUrl {
    url: String,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
#[serde(untagged)]
enum OpenAiMessageContent {
    Text(String),
    Parts(Vec<OpenAiContentPart>),
}

#[derive(Serialize, Deserialize, Debug, Clone)]
struct OpenAiChatMessage {
    role: String,
    content: OpenAiMessageContent,
}

#[derive(Serialize, Debug)]
struct OpenAiChatCompletionRequest {
    model: String,
    messages: Vec<OpenAiChatMessage>,
    stream: bool,
}

#[derive(Deserialize, Debug)]
struct OpenAiChatCompletionChoice {
    message: OpenAiChatMessage,
    index: Option<u32>,
    finish_reason: Option<String>,
}

#[derive(Deserialize, Debug, Clone)]
struct ApiError {
    message: String,
}

#[derive(Deserialize, Debug)]
struct OpenAiChatCompletionResponse {
    id: Option<String>,
    object: Option<String>,
    created: Option<u64>,
    model: Option<String>,
    choices: Option<Vec<OpenAiChatCompletionChoice>>,
    error: Option<ApiError>,
}

// Google Gemini Chat Structs
#[derive(Serialize, Debug, Clone)]
struct GeminiSafetySetting {
    category: String,
    threshold: String,
}

#[derive(Serialize, Debug, Default, Clone)]
struct GeminiGenerationConfig {
}

#[derive(Serialize, Debug, Clone)]
struct GeminiInlineData {
    #[serde(rename = "mimeType")]
    mime_type: String,
    data: String,
}

#[derive(Serialize, Debug, Clone)]
struct GeminiRequestPart {
    #[serde(skip_serializing_if = "Option::is_none")]
    text: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    #[serde(rename = "inlineData")]
    inline_data: Option<GeminiInlineData>,
}

#[derive(Serialize, Debug, Clone)]
struct GeminiRequestContent {
    #[serde(skip_serializing_if = "Option::is_none")]
    role: Option<String>,
    parts: Vec<GeminiRequestPart>,
}

#[derive(Serialize, Debug)]
struct GeminiChatRequest {
    contents: Vec<GeminiRequestContent>,
    #[serde(skip_serializing_if = "Option::is_none")]
    generation_config: Option<GeminiGenerationConfig>,
    #[serde(skip_serializing_if = "Option::is_none")]
    safety_settings: Option<Vec<GeminiSafetySetting>>,
}

#[derive(Deserialize, Debug, Clone)]
struct GeminiResponseTextPart {
    text: String,
}
#[derive(Deserialize, Debug, Clone)]
struct GeminiResponseContent {
     parts: Vec<GeminiResponseTextPart>,
     role: Option<String>,
}

#[derive(Deserialize, Debug)]
struct GeminiCandidate {
    content: GeminiResponseContent,
}

#[derive(Deserialize, Debug)]
struct GeminiApiErrorDetail {
    code: Option<i32>,
    message: String,
    status: Option<String>,
}
#[derive(Deserialize, Debug)]
struct GeminiApiErrorStructure {
   error: GeminiApiErrorDetail,
}

#[derive(Deserialize, Debug)]
struct GeminiChatResponse {
    candidates: Option<Vec<GeminiCandidate>>,
    error: Option<ApiError>,
}

// --- General Structs ---
#[derive(Serialize, Deserialize, Debug, Clone)]
struct ImageDialogResponse { file_path: Option<PathBuf>, file_name: Option<String>, mime_type: Option<String>, base64_content: Option<String>, data_url: Option<String> }

#[derive(Serialize, Deserialize, Debug, Default, Clone)]
struct ApiKeys {
    openrouter_key: Option<String>,
    gemini_key: Option<String>,
    ollama_url: Option<String>,
    lm_studio_url: Option<String>,
}

// For open_file_and_read_content command
#[derive(Serialize, Deserialize, Debug, Clone)]
struct OpenedFileContent {
    path: String,
    content: String,
    language: String, // Determined from file extension
}

#[derive(Serialize, Deserialize, Debug, Clone)]
struct CodeFile {
    path: String,
    content: String,
}


const SETTINGS_FILE_NAME: &str = "settings.json";
const DEFAULT_OLLAMA_URL: &str = "http://localhost:11434";
const DEFAULT_LM_STUDIO_URL: &str = "http://localhost:1234/v1";


fn get_settings_path(app_handle: &AppHandle) -> Result<PathBuf, String> {
    let base_dir = app_handle.path().app_data_dir()
        .map_err(|e: tauri::Error| format!("Failed to get app data directory: {}", e.to_string()))?;
    Ok(base_dir.join(SETTINGS_FILE_NAME))
}

#[tauri::command]
async fn save_api_keys(
    app_handle: AppHandle,
    keys: ApiKeys,
) -> Result<(), String> {
    let path = get_settings_path(&app_handle)?;
    if let Some(parent_dir) = path.parent() {
        fs::create_dir_all(parent_dir)
            .map_err(|e| format!("Failed to create settings directory ({}): {}", parent_dir.display(), e))?;
    }
    let json_string = serde_json::to_string_pretty(&keys)
        .map_err(|e| format!("Failed to serialize API keys: {}", e))?;
    fs::write(&path, json_string)
        .map_err(|e| format!("Failed to write settings file ({}): {}", path.display(), e))
}

#[tauri::command]
async fn load_api_keys(
    app_handle: AppHandle,
) -> Result<ApiKeys, String> {
    let path = get_settings_path(&app_handle)?;
    if path.exists() {
        let json_string = fs::read_to_string(&path)
            .map_err(|e| format!("Failed to read settings file ({}): {}", path.display(), e))?;
        serde_json::from_str(&json_string)
            .map_err(|e| format!("Failed to deserialize API keys from settings file: {}", e))
    } else {
        Ok(ApiKeys::default())
    }
}

#[tauri::command]
async fn get_openrouter_models(app_handle: AppHandle) -> Result<Vec<String>, String> {
    let keys = load_api_keys(app_handle.clone()).await?;
    let api_key = keys.openrouter_key.ok_or_else(|| "OpenRouter API key not set. Please configure it in Settings.".to_string())?;
    let client = reqwest::Client::new();
    match client.get("https://openrouter.ai/api/v1/models")
        .bearer_auth(api_key)
        .send().await {
        Ok(response) => {
            let status = response.status();
            if status.is_success() {
                match response.json::<OpenRouterModelsResponse>().await {
                    Ok(data) => Ok(data.data.into_iter().map(|m| m.id).collect()),
                    Err(e) => Err(format!("Failed to parse OpenRouter models JSON: {}", e)),
                }
            } else {
                let error_body = response.text().await.unwrap_or_else(|_| String::from("Could not get response text"));
                Err(format!("OpenRouter API request for models failed with status: {}. Response: {:?}", status, error_body))
            }
        }
        Err(e) => Err(format!("Failed to connect to OpenRouter API for models: {}", e)),
    }
}

#[tauri::command]
async fn get_google_gemini_models(app_handle: AppHandle) -> Result<Vec<String>, String> {
    let keys = load_api_keys(app_handle.clone()).await?;
    let api_key = keys.gemini_key.ok_or_else(|| "Google Gemini API key not set. Please configure it in Settings.".to_string())?;
    let client = reqwest::Client::new();
    let url = format!("https://generativelanguage.googleapis.com/v1beta/models?key={}", api_key);
    match client.get(&url).send().await {
        Ok(response) => {
            let status = response.status();
            if status.is_success() {
                match response.json::<GeminiModelsResponse>().await {
                    Ok(data) => Ok(data.models.into_iter().map(|m| m.name).collect()),
                    Err(e) => Err(format!("Failed to parse Google Gemini models JSON: {}", e)),
                }
            } else {
                 let error_body = response.text().await.unwrap_or_else(|_| "Could not get error response text".to_string());
                 Err(format!("Google Gemini API request for models failed with status: {}. Response: {:?}", status, error_body))
            }
        }
        Err(e) => Err(format!("Failed to connect to Google Gemini API for models: {}", e)),
    }
}

#[tauri::command]
async fn select_image_file(window: Window) -> Result<ImageDialogResponse, String> {
    let (tx, rx) = oneshot::channel::<Result<ImageDialogResponse, String>>();
    window.dialog().file()
        .add_filter("Images", &["png", "jpeg", "jpg", "webp", "gif"])
        .pick_file(move |file_path_option: Option<FilePath>| {
            if let Some(fp_variant) = file_path_option {
                match fp_variant {
                    FilePath::Path(path_buf) => {
                        let file_name = path_buf.file_name().unwrap_or_default().to_string_lossy().to_string();
                        let result = (|| {
                            let mut file = fs::File::open(&path_buf).map_err(|e| format!("Failed to open file: {}", e))?;
                            let mut buffer = Vec::new();
                            file.read_to_end(&mut buffer).map_err(|e| format!("Failed to read file: {}", e))?;
                            let base64_content = BASE64_STANDARD.encode(&buffer);
                            let mime_type = match path_buf.extension().and_then(|ext_os| ext_os.to_str().map(|s| s.to_lowercase())) {
                                Some(ext) if ext == "png" => "image/png",
                                Some(ext) if ext == "jpg" || ext == "jpeg" => "image/jpeg",
                                Some(ext) if ext == "gif" => "image/gif",
                                Some(ext) if ext == "webp" => "image/webp",
                                _ => "application/octet-stream",
                            }.to_string();
                            let data_url = format!("data:{};base64,{}", mime_type, base64_content);
                            Ok(ImageDialogResponse {
                                file_path: Some(path_buf),
                                file_name: Some(file_name),
                                mime_type: Some(mime_type),
                                base64_content: Some(base64_content),
                                data_url: Some(data_url)
                            })
                        })();
                        let _ = tx.send(result);
                    }
                    _ => {
                         let _ = tx.send(Err("Unexpected FilePath variant received from dialog.".to_string()));
                    }
                }
            } else {
                let _ = tx.send(Ok(ImageDialogResponse {
                    file_path: None, file_name: None, mime_type: None, base64_content: None, data_url: None
                }));
            }
        });
    match rx.await {
        Ok(result) => result,
        Err(_) => Err("Failed to receive file dialog result from callback.".to_string()),
    }
}

#[tauri::command]
async fn get_ollama_models(app_handle: AppHandle) -> Result<Vec<String>, String> {
    let keys = load_api_keys(app_handle.clone()).await?;
    let base_url = keys.ollama_url.unwrap_or_else(|| DEFAULT_OLLAMA_URL.to_string());
    let url = format!("{}/api/tags", base_url);

    let client = reqwest::Client::new();
    match client.get(&url).send().await {
        Ok(response) => {
            let status = response.status();
            if status.is_success() {
                match response.json::<OllamaTagsResponse>().await {
                    Ok(data) => Ok(data.models.into_iter().map(|m| m.name).collect()),
                    Err(e) => Err(format!("Failed to parse Ollama models JSON from {}: {}", url, e)),
                }
            } else {
                let error_body = response.text().await.unwrap_or_else(|_| String::from("Could not get response text"));
                Err(format!("Ollama API request to {} failed with status: {}. Response: {:?}", url, status, error_body))
            }
        }
        Err(e) => Err(format!("Failed to connect to Ollama API at {}: {}", url, e)),
    }
}

#[tauri::command]
async fn get_lm_studio_models(app_handle: AppHandle) -> Result<Vec<String>, String> {
    let keys = load_api_keys(app_handle.clone()).await?;
    let base_url = keys.lm_studio_url.unwrap_or_else(|| DEFAULT_LM_STUDIO_URL.to_string());
    let url = format!("{}/models", base_url);

    let client = reqwest::Client::new();
    match client.get(&url).send().await {
        Ok(response) => {
            let status = response.status();
            if status.is_success() {
                match response.json::<LmStudioModelsResponse>().await {
                    Ok(data) => Ok(data.data.into_iter().map(|m| m.id).collect()),
                    Err(e) => Err(format!("Failed to parse LM Studio models JSON from {}: {}", url, e)),
                }
            } else {
                let error_body = response.text().await.unwrap_or_else(|_| String::from("Could not get response text"));
                Err(format!("LM Studio API request to {} failed with status: {}. Response: {:?}", url, status, error_body))
            }
        }
        Err(e) => Err(format!("Failed to connect to LM Studio API at {}: {}", url, e)),
    }
}

#[tauri::command]
async fn save_new_code_file(
    _app_handle: AppHandle, // Prefixed unused app_handle
    window: Window,
    filename_suggestion: String,
    content: String,
) -> Result<String, String> {
    let dialog = window.dialog().file().set_file_name(&filename_suggestion);

    let (tx, rx) = oneshot::channel::<Option<PathBuf>>();
    dialog.save_file(move |path_option: Option<FilePath>| {
        let path_to_send: Option<PathBuf> = match path_option {
            Some(FilePath::Path(pb)) => Some(pb),
            _ => None,
        };
        let _ = tx.send(path_to_send);
    });

    match rx.await {
        Ok(Some(path_buf)) => {
            match fs::write(&path_buf, content) {
                Ok(_) => Ok(path_buf.to_string_lossy().into_owned()),
                Err(e) => Err(format!("Failed to write to file {}: {}", path_buf.display(), e)),
            }
        }
        Ok(None) => Err("File save cancelled by user.".to_string()),
        Err(_) => Err("Failed to get result from save file dialog.".to_string()),
    }
}

#[tauri::command]
async fn open_file_and_read_content(window: Window) -> Result<OpenedFileContent, String> {
    let (tx, rx) = oneshot::channel::<Option<PathBuf>>();

    window.dialog().file().pick_file(move |file_path_option: Option<FilePath>| { // Callback provides Option<FilePath>
        let path_to_send: Option<PathBuf> = match file_path_option {
            Some(FilePath::Path(pb)) => Some(pb),
            _ => None, // Handles Some(FilePath::Directory) or None from dialog (pick_file should only give Path or None)
        };
        tx.send(path_to_send).expect("Failed to send file path from dialog callback");
    });

    match rx.await {
        Ok(Some(path_buf)) => {
            let path_str = path_buf.to_string_lossy().into_owned();
            match fs::read_to_string(&path_buf) {
                Ok(content) => {
                    let language = match path_buf.extension().and_then(std::ffi::OsStr::to_str).map(|s| s.to_lowercase()) {
                        Some(ext) if ext == "js" => "javascript",
                        Some(ext) if ext == "ts" => "typescript",
                        Some(ext) if ext == "tsx" => "typescript", // Monaco uses 'typescript' for tsx too
                        Some(ext) if ext == "py" => "python",
                        Some(ext) if ext == "rs" => "rust",
                        Some(ext) if ext == "html" => "html",
                        Some(ext) if ext == "css" => "css",
                        Some(ext) if ext == "json" => "json",
                        Some(ext) if ext == "md" => "markdown",
                        Some(ext) if ext == "ps1" => "powershell",
                        // Add more mappings as needed
                        _ => "plaintext", // Default
                    }.to_string();

                    Ok(OpenedFileContent {
                        path: path_str,
                        content,
                        language,
                    })
                }
                Err(e) => Err(format!("Failed to read file {}: {}", path_str, e)),
            }
        }
        Ok(None) => Err("File selection cancelled by user.".to_string()), // User cancelled
        Err(_) => Err("Failed to get result from open file dialog channel.".to_string()), // Channel error
    }
}

#[tauri::command]
async fn overwrite_file_content(
    _app_handle: AppHandle, // Mark as unused if not needed
    _window: Window,      // Mark as unused if not needed
    path: String,
    content: String,
) -> Result<(), String> {
    fs::write(&path, content)
        .map_err(|e| format!("Failed to write to file {}: {}", path, e))
}

#[tauri::command]
async fn select_code_entity(window: Window) -> Result<Vec<CodeFile>, String> {
    let (tx, rx) = oneshot::channel::<Result<Vec<CodeFile>, String>>();

    // Ask user to pick a file or directory. For simplicity, let's start with just picking a single file
    // and then expand to directory picking. Or, offer two separate dialogs.
    // For now, let's use pick_file which can also select directories if the OS dialog supports it,
    // but it's more common for picking individual files.
    // A more robust solution would be two buttons on UI: "Upload File", "Upload Folder".
    // Let's assume for now we try to pick one entity and determine if it's a file or dir.
    // Define common code file extensions
    const CODE_EXTENSIONS: &[&str] = &[
        "txt", "py", "js", "ts", "tsx", "rs", "html", "css", "scss", "json", "md",
        "java", "c", "cpp", "h", "hpp", "cs", "go", "php", "rb", "swift", "kt",
        "kts", "yaml", "yml", "toml", "sh", "bash", "ps1", "sql", "xml", "vue",
        "svelte", "dart", "lua", "pl", "r", "scala", "groovy", "clj", "cljs", "edn"
    ];

    window.dialog().file().pick_folder(move |folder_path_option: Option<FilePath>| {
        if let Some(FilePath::Path(folder_path)) = folder_path_option {
            let mut files_to_send = Vec::new();
            let mut processing_errors = Vec::new();

            const IGNORED_DIRS: &[&str] = &["node_modules", ".git", ".vscode", "target", "dist", "out", "build"]; // Common directories to ignore

            for entry_result in WalkDir::new(&folder_path)
                .into_iter()
                .filter_entry(|e| { // Filter entries before trying to read them
                    let path = e.path();
                    if path.is_dir() {
                        return !IGNORED_DIRS.iter().any(|ignored_dir| path.ends_with(ignored_dir));
                    }
                    true // Keep files for now, extension check will happen later
                })
            {
                let entry = match entry_result {
                    Ok(entry) => entry,
                    Err(e) => {
                        processing_errors.push(format!("Error accessing path {}: {}", e.path().unwrap_or_else(|| Path::new("unknown")).display(), e));
                        continue;
                    }
                };

                let path = entry.path();
                if path.is_file() {
                    if let Some(ext) = path.extension().and_then(|s| s.to_str()) {
                        if CODE_EXTENSIONS.contains(&ext.to_lowercase().as_str()) {
                            match fs::read_to_string(path) {
                                Ok(content) => {
                                    files_to_send.push(CodeFile {
                                        path: path.to_string_lossy().into_owned(),
                                        content,
                                    });
                                }
                                Err(e) => {
                                    processing_errors.push(format!("Failed to read file {}: {}", path.display(), e));
                                }
                            }
                        }
                    }
                }
            }

            if !processing_errors.is_empty() {
                // For now, send successfully read files along with a concatenated error string for failures.
                // A more robust solution might involve a structured response with successes and failures.
                eprintln!("Errors during folder processing: {}", processing_errors.join("\n"));
                // If no files were successfully read but errors occurred, return the error.
                if files_to_send.is_empty() {
                    let _ = tx.send(Err(processing_errors.join("\n")));
                    return;
                }
                // Otherwise, send what was successful, errors are logged on backend for now.
            }

            if files_to_send.is_empty() && processing_errors.is_empty() {
                // No code files found or folder was empty
                 let _ = tx.send(Err("No recognized code files found in the selected folder.".to_string()));
            } else {
                 let _ = tx.send(Ok(files_to_send));
            }

        } else {
            // User cancelled the dialog or selected nothing
            let _ = tx.send(Ok(Vec::new())); // Send empty vec for cancellation
        }
    });

    match rx.await {
        Ok(result) => result,
        Err(_) => Err("Failed to receive file dialog result from callback channel.".to_string()),
    }
}


#[tauri::command]
async fn send_chat_message(
    app_handle: AppHandle,
    provider: String,
    model_name: String,
    // prompt_text is now expected to be a JSON string of Vec<OllamaMessage> for Ollama provider
    // or the plain prompt string for other providers.
    prompt_text: String,
    // image_data_url is now primarily for non-Ollama providers,
    // as Ollama images should be embedded in the messages_json.
    // However, we can keep it for a simple last-message image attachment for Ollama if messages_json is not used.
    image_data_url: Option<String>,
) -> Result<String, String> {
    let client = reqwest::Client::new();
    let keys = load_api_keys(app_handle.clone()).await?;

    if provider == "Ollama" {
        let base_url = keys.ollama_url.unwrap_or_else(|| DEFAULT_OLLAMA_URL.to_string());
        let url = format!("{}/api/chat", base_url);

        // Deserialize prompt_text into Vec<OllamaMessage>
        // If deserialization fails, it means prompt_text was likely a plain string for a single message.
        let messages_to_send: Vec<OllamaMessage> = match serde_json::from_str(&prompt_text) {
            Ok(parsed_messages) => parsed_messages,
            Err(_) => {
                // Assume prompt_text is a simple string for a single user message
                let mut images_base64: Option<Vec<String>> = None;
                if let Some(data_url) = image_data_url {
                    if let Some(base64_part) = data_url.split(',').nth(1) {
                        images_base64 = Some(vec![base64_part.to_string()]);
                    }
                }
                vec![OllamaMessage {
                    role: "user".to_string(),
                    content: prompt_text.clone(), // Use the original prompt_text
                    images: images_base64,
                }]
            }
        };

        let request_payload = OllamaChatRequest {
            model: model_name,
            messages: messages_to_send,
            stream: false
        };

        match client.post(&url).json(&request_payload).send().await {
            Ok(response) => {
                let status = response.status();
                if status.is_success() {
                    match response.json::<OllamaChatResponse>().await {
                        Ok(data) => {
                            if let Some(error_msg) = data.error {
                                Err(format!("Ollama API error: {}", error_msg))
                            } else if let Some(message_obj) = data.message {
                                Ok(message_obj.content) // Return the content of the assistant's message
                            } else {
                                Err("Ollama /api/chat response missing 'message' object or content.".to_string())
                            }
                        }
                        Err(e) => Err(format!("Failed to parse Ollama /api/chat response JSON from {}: {}", url, e)),
                    }
                } else {
                    let error_body = response.text().await.unwrap_or_default();
                    Err(format!("Ollama API /api/chat request to {} failed with status: {}. Response: {:?}", url, status, error_body))
                }
            }
            Err(e) => Err(format!("Failed to send message to Ollama at {}: {}", url, e)),
        }
    } else if provider == "LM Studio" {
        let base_url = keys.lm_studio_url.unwrap_or_else(|| DEFAULT_LM_STUDIO_URL.to_string());
        let url = format!("{}/chat/completions", base_url);
        let mut request_content_parts = vec![OpenAiContentPart {
            part_type: "text".to_string(),
            text: Some(prompt_text.clone()),
            image_url: None,
        }];
        if let Some(data_url) = image_data_url.clone() {
             request_content_parts.push(OpenAiContentPart {
                part_type: "image_url".to_string(),
                text: None,
                image_url: Some(OpenAiImageUrl { url: data_url }),
            });
        }
        let request_message = OpenAiChatMessage {
            role: "user".to_string(),
            content: OpenAiMessageContent::Parts(request_content_parts),
        };
        let request_payload = OpenAiChatCompletionRequest {
            model: model_name.clone(),
            messages: vec![request_message],
            stream: false,
        };
        match client.post(&url).json(&request_payload).send().await {
            Ok(response) => {
                let status = response.status();
                if status.is_success() {
                    match response.json::<OpenAiChatCompletionResponse>().await {
                        Ok(data) => {
                            if let Some(error_obj) = data.error { Err(format!("LM Studio API error: {}", error_obj.message)) }
                            else if let Some(choices) = data.choices {
                                if let Some(first_choice) = choices.get(0) {
                                    match &first_choice.message.content {
                                        OpenAiMessageContent::Text(text_content) => Ok(text_content.clone()),
                                        OpenAiMessageContent::Parts(parts) => parts.iter().find(|p| p.part_type == "text").and_then(|p| p.text.clone()).ok_or_else(|| "LM Studio response message (parts) had no text content.".to_string()),
                                    }
                                } else { Err("LM Studio response had no choices.".to_string()) }
                            } else { Err("LM Studio response missing 'choices' field.".to_string()) }
                        }
                        Err(e) => Err(format!("Failed to parse LM Studio response JSON from {}: {}", url, e)),
                    }
                } else {
                    let error_body = response.text().await.unwrap_or_default();
                    Err(format!("LM Studio API request to {} failed with status: {}. Response: {:?}", url, status, error_body))
                }
            }
            Err(e) => Err(format!("Failed to send message to LM Studio at {}: {}", url, e)),
        }
    } else if provider == "OpenRouter" {
        let api_key = keys.openrouter_key.ok_or_else(|| "OpenRouter API key not set. Please configure it in Settings.".to_string())?;
        let url = "https://openrouter.ai/api/v1/chat/completions";
        let mut request_content_parts = vec![OpenAiContentPart {
            part_type: "text".to_string(),
            text: Some(prompt_text.clone()),
            image_url: None,
        }];
        if let Some(data_url) = image_data_url.clone() {
             request_content_parts.push(OpenAiContentPart {
                part_type: "image_url".to_string(),
                text: None,
                image_url: Some(OpenAiImageUrl { url: data_url }),
            });
        }
        let request_message = OpenAiChatMessage {
            role: "user".to_string(),
            content: OpenAiMessageContent::Parts(request_content_parts),
        };
        let request_payload = OpenAiChatCompletionRequest {
            model: model_name.clone(),
            messages: vec![request_message],
            stream: false,
        };
        match client.post(url)
            .bearer_auth(api_key)
            .header("HTTP-Referer", "http://localhost")
            .header("X-Title", "OLugo Standalone App")
            .json(&request_payload)
            .send()
            .await
        {
            Ok(response) => {
                let status = response.status();
                if status.is_success() {
                    match response.json::<OpenAiChatCompletionResponse>().await {
                        Ok(data) => {
                            if let Some(error_obj) = data.error { Err(format!("OpenRouter API error: {}", error_obj.message)) }
                            else if let Some(choices) = data.choices {
                                if let Some(first_choice) = choices.get(0) {
                                    match &first_choice.message.content {
                                        OpenAiMessageContent::Text(text_content) => Ok(text_content.clone()),
                                        OpenAiMessageContent::Parts(parts) => parts.iter().find(|p| p.part_type == "text").and_then(|p| p.text.clone()).ok_or_else(|| "OpenRouter response message (parts) had no text content.".to_string()),
                                    }
                                } else { Err("OpenRouter response had no choices.".to_string()) }
                            } else { Err("OpenRouter response missing 'choices' field.".to_string()) }
                        }
                        Err(e) => Err(format!("Failed to parse OpenRouter response JSON: {}", e)),
                    }
                } else {
                    let error_body = response.text().await.unwrap_or_default();
                    Err(format!("OpenRouter API request failed with status: {}. Response: {:?}", status, error_body))
                }
            }
            Err(e) => Err(format!("Failed to send message to OpenRouter: {}", e)),
        }
    } else if provider == "Google Gemini" {
        let api_key = keys.gemini_key.ok_or_else(|| "Google Gemini API key not set. Please configure it in Settings.".to_string())?;
        let url = format!("https://generativelanguage.googleapis.com/v1beta/{}:generateContent?key={}", model_name, api_key);

        let mut gemini_parts_payload = Vec::new();
        gemini_parts_payload.push(GeminiRequestPart {
            text: Some(prompt_text),
            inline_data: None,
        });

        if let Some(data_url_str) = image_data_url {
            if let Some(comma_index) = data_url_str.find(',') {
                let header = &data_url_str[..comma_index];
                let base64_data = &data_url_str[comma_index+1..];
                let mime_type_part = header.strip_prefix("data:").and_then(|s| s.split(';').next());
                if let Some(mime_type) = mime_type_part {
                    if mime_type.starts_with("image/") {
                         gemini_parts_payload.push(GeminiRequestPart {
                            text: None,
                            inline_data: Some(GeminiInlineData {
                                mime_type: mime_type.to_string(),
                                data: base64_data.to_string(),
                            }),
                        });
                    } else {
                        eprintln!("Warning: Invalid image MIME type for Gemini: {}", mime_type);
                    }
                }
            }
        }

        let gemini_content = GeminiRequestContent {
            role: Some("user".to_string()),
            parts: gemini_parts_payload,
        };
        let request_payload = GeminiChatRequest {
            contents: vec![gemini_content],
            generation_config: Some(GeminiGenerationConfig::default()),
            safety_settings: None,
        };

        match client.post(&url).json(&request_payload).send().await {
            Ok(response) => {
                let status = response.status();
                if status.is_success() {
                    match response.json::<GeminiChatResponse>().await {
                        Ok(data) => {
                            if let Some(api_err) = data.error {
                                return Err(format!("Google Gemini API error: {}", api_err.message));
                            }
                            if let Some(candidates) = data.candidates {
                                if let Some(first_candidate) = candidates.get(0) {
                                    if let Some(first_part) = first_candidate.content.parts.get(0) {
                                        Ok(first_part.text.clone())
                                    } else {
                                        Err("Google Gemini response missing text part in candidate content.".to_string())
                                    }
                                } else {
                                    Err("Google Gemini response had no candidates.".to_string())
                                }
                            } else {
                                Err("Google Gemini response missing 'candidates' field and no top-level error.".to_string())
                            }
                        }
                        Err(e) => Err(format!("Failed to parse Google Gemini response JSON: {}", e)),
                    }
                } else {
                    let error_body = response.text().await.unwrap_or_else(|_| "Could not get error response text".to_string());
                    if let Ok(gemini_api_err) = serde_json::from_str::<GeminiApiErrorStructure>(&error_body) {
                        Err(format!("Google Gemini API request failed with status: {}. Error: {}", status, gemini_api_err.error.message))
                    } else {
                        Err(format!("Google Gemini API request failed with status: {}. Response: {:?}", status, error_body))
                    }
                }
            }
            Err(e) => Err(format!("Failed to send message to Google Gemini ({}): {}", url, e)),
        }
    } else {
        Err(format!("Unsupported provider: {}", provider))
    }
}

#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    // Create terminal state
    // let terminal_state = terminal::create_terminal_state();

    // Configure the PTY plugin with permissions
    // let pty_plugin = tauri_plugin_pty::init(); // This might be useful for the new terminal too // REMOVED

    tauri::Builder::default()
        .setup(|app| {
            let yk_terminal_state = yakuake_terminal::YakuakeTerminalState::new(app.handle().clone());
            app.manage(yk_terminal_state);
            Ok(())
        })
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_dialog::init())
        .plugin(tauri_plugin_fs::init())
        // .plugin(pty_plugin) // REMOVED
        // .manage(terminal_state) // Old terminal state
        .invoke_handler(tauri::generate_handler![
            greet,
            get_ollama_models,
            get_lm_studio_models,
            send_chat_message,
            select_image_file,
            save_api_keys,
            load_api_keys,
            get_openrouter_models,
            get_google_gemini_models,
            save_new_code_file,
            open_file_and_read_content,
            overwrite_file_content,
            select_code_entity, // Added new command
            // Yakuake Terminal commands
            yakuake_terminal::yk_create_terminal,
            yakuake_terminal::yk_write_to_terminal,
            yakuake_terminal::yk_resize_terminal,
            yakuake_terminal::yk_close_terminal,
            yakuake_terminal::yk_send_command_to_shell
            // Old Terminal commands (commented out)
            // terminal::create_terminal_shell,
            // terminal::write_to_terminal,
            // terminal::read_from_terminal,
            // terminal::resize_terminal,
            // Old Node.js terminal commands (commented out)
            // node_terminal::create_node_terminal,
            // node_terminal::write_to_node_terminal,
            // node_terminal::resize_node_terminal,
            // node_terminal::kill_node_terminal,
            // node_terminal::list_node_terminals
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
