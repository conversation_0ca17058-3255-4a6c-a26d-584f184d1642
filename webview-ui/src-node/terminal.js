// Node.js terminal service using node-pty
const os = require('os');
const pty = require('node-pty');

// Map of terminal instances
const terminals = new Map();

// Generate a unique terminal ID
function generateTerminalId() {
  return `term_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
}

// Create a new terminal process
function createTerminal(emit) {
  console.log('Creating new terminal process...');

  // Determine shell based on platform
  const platform = os.platform();
  console.log('Platform:', platform);

  const shell = platform === 'win32' ? 'powershell.exe' :
                platform === 'darwin' ? '/bin/zsh' :
                '/bin/bash';
  console.log('Selected shell:', shell);

  // Shell arguments
  const shellArgs = platform === 'win32' ? [] : ['-l'];
  console.log('Shell arguments:', shellArgs);

  // Current working directory
  const cwd = process.cwd();
  console.log('Current working directory:', cwd);

  try {
    console.log('Spawning PTY process...');
    // Create terminal process
    const ptyProcess = pty.spawn(shell, shellArgs, {
      name: 'xterm-color',
      cols: 80,
      rows: 24,
      cwd,
      env: process.env
    });
    console.log('PTY process spawned successfully');

    // Generate a unique ID for this terminal
    const terminalId = generateTerminalId();
    console.log('Generated terminal ID:', terminalId);

    // Set up data handler
    console.log('Setting up data handler...');
    ptyProcess.onData((data) => {
      console.log(`Terminal ${terminalId} data:`, JSON.stringify(data));
      // Emit data event to the frontend
      emit('terminal-data', { terminalId, data });
    });
    console.log('Data handler set up successfully');

    // Set up exit handler
    console.log('Setting up exit handler...');
    ptyProcess.onExit(({ exitCode, signal }) => {
      console.log(`Terminal ${terminalId} exited with code ${exitCode}, signal ${signal}`);
      // Emit exit event to the frontend
      emit('terminal-exit', { terminalId, exitCode, signal });

      // Clean up
      console.log(`Removing terminal ${terminalId} from terminals map`);
      terminals.delete(terminalId);
    });
    console.log('Exit handler set up successfully');

    // Store the terminal instance
    console.log(`Storing terminal ${terminalId} in terminals map`);
    terminals.set(terminalId, ptyProcess);

    // Write a test message to the terminal
    console.log('Writing test message to terminal...');
    ptyProcess.write('echo "Terminal initialized successfully"\r');
    console.log('Test message written successfully');

    return { terminalId };
  } catch (error) {
    console.error('Error creating terminal process:', error);
    throw error;
  }
}

// Write data to a terminal
function writeToTerminal(terminalId, data) {
  console.log(`Writing data to terminal ${terminalId}:`, JSON.stringify(data));

  // Check if the terminal exists
  console.log(`Checking if terminal ${terminalId} exists...`);
  const terminal = terminals.get(terminalId);

  if (!terminal) {
    console.error(`Terminal with ID ${terminalId} not found`);
    console.log('Available terminals:', Array.from(terminals.keys()));
    throw new Error(`Terminal with ID ${terminalId} not found`);
  }

  console.log(`Terminal ${terminalId} found, writing data...`);
  try {
    terminal.write(data);
    console.log(`Data written to terminal ${terminalId} successfully`);
  } catch (error) {
    console.error(`Error writing to terminal ${terminalId}:`, error);
    throw error;
  }
}

// Resize a terminal
function resizeTerminal(terminalId, cols, rows) {
  const terminal = terminals.get(terminalId);

  if (!terminal) {
    throw new Error(`Terminal with ID ${terminalId} not found`);
  }

  terminal.resize(cols, rows);
}

// Kill a terminal
function killTerminal(terminalId) {
  const terminal = terminals.get(terminalId);

  if (!terminal) {
    throw new Error(`Terminal with ID ${terminalId} not found`);
  }

  terminal.kill();
  terminals.delete(terminalId);
}

// List all terminals
function listTerminals() {
  return Array.from(terminals.keys()).map(terminalId => ({
    terminalId,
    // Add any other terminal info you want to expose
  }));
}

// Export the terminal API
module.exports = {
  createTerminal,
  writeToTerminal,
  resizeTerminal,
  killTerminal,
  listTerminals
};
