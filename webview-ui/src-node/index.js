// Node.js integration for Tauri
const { invoke } = require('@tauri-apps/api/tauri');
const { listen } = require('@tauri-apps/api/event');
// const terminal = require('./terminal');

// Listen for terminal events from Tauri
async function setupTerminalListeners() {
  console.log('Setting up Node.js terminal listeners...');

  // Create terminal
  console.log('Setting up node-terminal-create listener...');
  await listen('node-terminal-create', async () => {
    console.log('Received node-terminal-create event');
    try {
      console.log('Creating terminal process...');
      const result = terminal.createTerminal((eventName, payload) => {
        console.log(`Terminal event: ${eventName}`, payload);
        // Emit events back to the frontend
        console.log(`Emitting ${eventName} event to frontend`);
        invoke('tauri', {
          __tauriModule: 'Event',
          message: {
            cmd: 'emit',
            event: eventName,
            payload
          }
        }).then(() => {
          console.log(`Successfully emitted ${eventName} event to frontend`);
        }).catch(err => {
          console.error(`Error emitting ${eventName} event to frontend:`, err);
        });
      });

      console.log('Terminal created with ID:', result.terminalId);

      // Send the terminal ID back to the frontend
      console.log('Sending terminal-created event to frontend');
      invoke('tauri', {
        __tauriModule: 'Event',
        message: {
          cmd: 'emit',
          event: 'terminal-created',
          payload: { terminalId: result.terminalId }
        }
      }).then(() => {
        console.log('Successfully sent terminal-created event to frontend');
      }).catch(err => {
        console.error('Error sending terminal-created event to frontend:', err);
      });

      // Send some test data to verify the terminal is working
      setTimeout(() => {
        try {
          console.log('Sending test data to terminal');
          terminal.writeToTerminal(result.terminalId, 'Terminal is ready. You can type commands now.\r\n');
          console.log('Test data sent successfully');
        } catch (err) {
          console.error('Error sending test data to terminal:', err);
        }
      }, 1000);

      return result;
    } catch (error) {
      console.error('Error creating terminal:', error);

      // Send error back to the frontend
      console.log('Sending terminal-error event to frontend');
      invoke('tauri', {
        __tauriModule: 'Event',
        message: {
          cmd: 'emit',
          event: 'terminal-error',
          payload: { error: error.message || String(error) }
        }
      }).then(() => {
        console.log('Successfully sent terminal-error event to frontend');
      }).catch(err => {
        console.error('Error sending terminal-error event to frontend:', err);
      });

      throw error;
    }
  });
  console.log('node-terminal-create listener set up successfully');

  // Write to terminal
  console.log('Setting up node-terminal-write listener...');
  await listen('node-terminal-write', async (event) => {
    console.log('Received node-terminal-write event:', event);
    try {
      const { terminalId, data } = event.payload;
      console.log(`Writing data to terminal ${terminalId}:`, JSON.stringify(data));
      terminal.writeToTerminal(terminalId, data);
      console.log(`Successfully wrote data to terminal ${terminalId}`);
    } catch (error) {
      console.error('Error writing to terminal:', error);

      // Send error back to the frontend
      console.log('Sending terminal-error event to frontend');
      invoke('tauri', {
        __tauriModule: 'Event',
        message: {
          cmd: 'emit',
          event: 'terminal-error',
          payload: {
            terminalId: event.payload?.terminalId,
            error: error.message || String(error)
          }
        }
      }).then(() => {
        console.log('Successfully sent terminal-error event to frontend');
      }).catch(err => {
        console.error('Error sending terminal-error event to frontend:', err);
      });
    }
  });
  console.log('node-terminal-write listener set up successfully');

  // Resize terminal
  await listen('node-terminal-resize', async (event) => {
    try {
      const { terminalId, cols, rows } = event.payload;
      terminal.resizeTerminal(terminalId, cols, rows);
    } catch (error) {
      console.error('Error resizing terminal:', error);
    }
  });

  // Kill terminal
  await listen('node-terminal-kill', async (event) => {
    try {
      const { terminalId } = event.payload;
      terminal.killTerminal(terminalId);
    } catch (error) {
      console.error('Error killing terminal:', error);
    }
  });

  // List terminals
  await listen('node-terminal-list', async () => {
    try {
      const terminals = terminal.listTerminals();

      // Send the terminal list back to the frontend
      invoke('tauri', {
        __tauriModule: 'Event',
        message: {
          cmd: 'emit',
          event: 'terminal-list',
          payload: { terminals }
        }
      }).catch(console.error);

      return terminals;
    } catch (error) {
      console.error('Error listing terminals:', error);
      throw error;
    }
  });
}

// Initialize the Node.js integration
async function initialize() {
  try {
    console.log('Initializing Node.js integration for Tauri...');
    await setupTerminalListeners();
    console.log('Node.js integration initialized successfully');
  } catch (error) {
    console.error('Error initializing Node.js integration:', error);
  }
}

// Start initialization
// initialize();
