{"name": "webview-ui", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@monaco-editor/react": "^4.7.0", "@tauri-apps/api": "^2.5.0", "@tauri-apps/plugin-dialog": "~2.0.0-beta.8", "@tauri-apps/plugin-fs": "~2.0.0-beta.8", "highlight.js": "^11.11.1", "markdown-it": "^14.1.0", "next": "15.3.2", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/markdown-it": "^14.1.2", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "typescript": "^5"}}