'use client';

import React, { useEffect, useRef, useState } from 'react';
import { invoke } from '../lib/tauri-api.ts';
import styles from './Terminal.module.css';

// Simple debounce function implementation
function debounce(func: Function, wait: number) {
  let timeout: NodeJS.Timeout | null = null;
  return function(...args: any[]) {
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// Type definitions for xterm.js
type XTerm = any;
type FitAddon = any;

interface TerminalProps {
  isVisible: boolean;
  onClose: () => void;
}

const Terminal: React.FC<TerminalProps> = ({ isVisible, onClose }) => {
  const terminalRef = useRef<HTMLDivElement>(null);
  const [term, setTerm] = useState<XTerm | null>(null);
  const [fitAddon, setFitAddon] = useState<FitAddon | null>(null);
  const [isResizing, setIsResizing] = useState(false);
  const [height, setHeight] = useState(300); // Default height
  const resizeStartYRef = useRef(0);
  const initialHeightRef = useRef(0);

  // Track if shell has been created
  const [shellCreated, setShellCreated] = useState(false);

  // Initialize terminal
  useEffect(() => {
    if (!terminalRef.current || !isVisible) return;

    console.log("Initializing terminal...");

    // Clean up any existing terminal
    if (term) {
      term.dispose();
      setTerm(null);
      setFitAddon(null);
    }

    let newTerm: XTerm | null = null;
    let newFitAddon: FitAddon | null = null;
    let isComponentMounted = true;

    // Dynamically import xterm.js and addons
    const initTerminal = async () => {
      try {
        // Dynamically import the CSS
        await import('xterm/css/xterm.css');

        // Dynamically import the modules
        const xtermModule = await import('xterm');
        const fitAddonModule = await import('xterm-addon-fit');
        const webLinksAddonModule = await import('xterm-addon-web-links');

        const XTerm = xtermModule.Terminal;
        const FitAddon = fitAddonModule.FitAddon;
        const WebLinksAddon = webLinksAddonModule.WebLinksAddon;

        // Create terminal instance with simplified settings
        newTerm = new XTerm({
          fontFamily: 'monospace',
          fontSize: 14,
          theme: {
            background: '#1a1f2e',
            foreground: '#f8f8f8',
            cursor: '#f8f8f8',
            black: '#000000',
            red: '#e06c75',
            green: '#98c379',
            yellow: '#e5c07b',
            blue: '#61afef',
            magenta: '#c678dd',
            cyan: '#56b6c2',
            white: '#dcdfe4',
            brightBlack: '#5c6370',
            brightRed: '#e06c75',
            brightGreen: '#98c379',
            brightYellow: '#e5c07b',
            brightBlue: '#61afef',
            brightMagenta: '#c678dd',
            brightCyan: '#56b6c2',
            brightWhite: '#ffffff',
          },
          cursorBlink: true,
          scrollback: 1000,
          convertEol: true,
          disableStdin: false,
          allowProposedApi: true,
          allowTransparency: false,
          rendererType: 'canvas',
        });

        // Create and load addons
        newFitAddon = new FitAddon();
        newTerm.loadAddon(newFitAddon);
        newTerm.loadAddon(new WebLinksAddon());

        // Open terminal in the container
        if (terminalRef.current && isComponentMounted) {
          console.log("Opening terminal in container");

          // Clear the terminal container first
          terminalRef.current.innerHTML = '';

          // Open the terminal
          newTerm.open(terminalRef.current);

          // Write a test message to ensure the terminal is working
          newTerm.writeln('Terminal initialized...');

          // Set state
          setTerm(newTerm);
          setFitAddon(newFitAddon);

          // Initialize shell if not already created
          if (!shellCreated) {
            try {
              console.log("Creating terminal shell");
              await invoke('create_terminal_shell');
              setShellCreated(true);
              console.log("Shell created successfully");
            } catch (error) {
              console.error('Error creating terminal shell:', error);
            }
          } else {
            console.log("Shell already created, not creating a new one");
          }

          // Wait for the terminal to be fully initialized before fitting
          // This is crucial to avoid the dimensions error
          setTimeout(() => {
            if (newFitAddon && isComponentMounted && newTerm && terminalRef.current) {
              try {
                // Make sure the terminal container has dimensions
                if (terminalRef.current.clientWidth > 0 && terminalRef.current.clientHeight > 0) {
                  // Set explicit dimensions first to avoid the error
                  const cols = Math.floor(terminalRef.current.clientWidth / 9); // Approximate character width
                  const rows = Math.floor(terminalRef.current.clientHeight / 17); // Approximate character height

                  // Set dimensions explicitly first
                  if (cols > 0 && rows > 0) {
                    newTerm.resize(cols, rows);
                    console.log(`Terminal resized to ${cols}x${rows}`);

                    // Notify the backend of the new terminal size
                    invoke('resize_terminal', { rows, cols })
                      .then(() => console.log(`Backend notified of resize to ${cols}x${rows}`))
                      .catch(error => console.error('Error notifying backend of resize:', error));

                    // Focus the terminal
                    newTerm.focus();
                    console.log("Terminal focused");
                  }
                } else {
                  console.warn("Terminal container has no dimensions yet");
                }
              } catch (e) {
                console.error("Error initializing terminal dimensions:", e);
              }
            }
          }, 300);
        }
      } catch (error) {
        console.error('Error initializing terminal:', error);
      }
    };

    initTerminal();

    // Clean up on unmount or when terminal is hidden
    return () => {
      console.log("Cleaning up terminal");
      isComponentMounted = false;
      if (newTerm) {
        newTerm.dispose();
      }
      setTerm(null);
      setFitAddon(null);
    };
  }, [isVisible, shellCreated]);

  // Handle terminal resize
  useEffect(() => {
    if (!term || !isVisible || !terminalRef.current) return;

    console.log("Setting up terminal resize handler");

    // Safe resize handler that avoids the dimensions error
    const safeResize = debounce(() => {
      try {
        if (terminalRef.current && terminalRef.current.clientWidth > 0 && terminalRef.current.clientHeight > 0) {
          // Calculate dimensions based on container size
          const cols = Math.floor(terminalRef.current.clientWidth / 9); // Approximate character width
          const rows = Math.floor(terminalRef.current.clientHeight / 17); // Approximate character height

          if (cols > 0 && rows > 0) {
            console.log(`Resizing terminal to ${cols}x${rows}`);

            // Resize the terminal directly instead of using fitAddon
            term.resize(cols, rows);

            // Notify the backend of the new terminal size
            invoke('resize_terminal', { rows, cols })
              .then(() => console.log(`Backend notified of resize to ${cols}x${rows}`))
              .catch(error => console.error('Error notifying backend of resize:', error));
          }
        }
      } catch (error) {
        console.error('Error resizing terminal:', error);
      }
    }, 100);

    // Initial resize after a delay
    setTimeout(safeResize, 300);

    // Listen for window resize
    window.addEventListener('resize', safeResize);

    // Clean up
    return () => {
      console.log("Removing terminal resize handler");
      window.removeEventListener('resize', safeResize);
    };
  }, [term, isVisible]);

  // Handle data input/output
  useEffect(() => {
    if (!term) return;

    console.log("Setting up terminal data handlers");

    // Write a test message directly to the terminal
    term.writeln('Terminal data handlers initialized...');
    term.writeln('Connecting to shell...');

    // Write data from terminal to PTY
    const dataHandler = (data: string) => {
      console.log("Terminal data:", data);

      // Send the data to the PTY
      invoke('write_to_terminal', { data })
        .then(() => {
          console.log("Data written to PTY");
        })
        .catch(error => {
          console.error('Error writing to terminal:', error);
          if (term) {
            term.write(`\r\nError: ${error}\r\n`);
          }
        });
    };

    // Attach the data handler to the terminal
    term.onData(dataHandler);

    // Read data from PTY
    let animationFrameId: number;
    let isReading = true;
    let readInterval: number | null = null;

    const readFromPty = async () => {
      if (!isReading || !term) return;

      try {
        const data = await invoke<string | null>('read_from_terminal');
        if (data && term && isReading) {
          console.log("Received data from PTY:", data);

          // Write the data to the terminal
          try {
            // Write the data to the terminal
            term.write(data);
            console.log("Successfully wrote data to terminal display");

            // Force a refresh of the terminal
            term.refresh(0, term.rows - 1);
          } catch (e) {
            console.error("Error writing to terminal display:", e);
          }
        }
      } catch (error) {
        console.error('Error reading from terminal:', error);

        // If there's an error, display it in the terminal
        if (term && isReading) {
          try {
            term.write(`\r\nError: ${error}\r\n`);
          } catch (e) {
            console.error("Error writing error message to terminal:", e);
          }
        }
      }
    };

    // Use both requestAnimationFrame and setInterval for more reliable polling
    const startReading = () => {
      // Use requestAnimationFrame for smooth UI updates
      const frameLoop = () => {
        if (isReading) {
          readFromPty();
          animationFrameId = requestAnimationFrame(frameLoop);
        }
      };
      frameLoop();

      // Also use setInterval as a backup to ensure we poll regularly
      readInterval = window.setInterval(() => {
        if (isReading) {
          readFromPty();
        }
      }, 100); // Poll every 100ms
    };

    // Send an initial carriage return to get the prompt to show
    setTimeout(() => {
      if (term && isReading) {
        console.log("Sending initial carriage return to get prompt");
        term.writeln('Initializing terminal...');
        invoke('write_to_terminal', { data: '\r\n' })
          .then(() => {
            console.log("Initial carriage return sent");
            // Send a clear command after a short delay
            setTimeout(() => {
              if (term && isReading) {
                invoke('write_to_terminal', { data: 'clear\r\n' })
                  .then(() => console.log("Clear command sent"))
                  .catch(error => console.error('Error sending clear command:', error));
              }
            }, 500);
          })
          .catch(error => console.error('Error sending initial carriage return:', error));
      }
    }, 500);

    // Start reading
    startReading();

    // Clean up
    return () => {
      console.log("Cleaning up terminal data handlers");
      isReading = false;

      // Clean up animation frame
      if (animationFrameId) {
        cancelAnimationFrame(animationFrameId);
      }

      // Clean up interval
      if (readInterval !== null) {
        clearInterval(readInterval);
      }

      // Dispose terminal if needed
      if (term && term.element && !term.element.isConnected) {
        term.dispose();
      }
    };
  }, [term]);

  // Handle keyboard shortcut (F12) to toggle terminal
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'F12') {
        e.preventDefault();
        if (isVisible) {
          onClose();
        } else {
          // This would be handled by the parent component
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [isVisible, onClose]);

  // Handle resizing
  const handleResizeStart = (e: React.MouseEvent) => {
    setIsResizing(true);
    resizeStartYRef.current = e.clientY;
    initialHeightRef.current = height;
  };

  useEffect(() => {
    const handleResizeMove = (e: MouseEvent) => {
      if (!isResizing) return;

      const deltaY = resizeStartYRef.current - e.clientY;
      const newHeight = Math.max(150, initialHeightRef.current + deltaY);
      setHeight(newHeight);
    };

    const handleResizeEnd = () => {
      setIsResizing(false);
    };

    if (isResizing) {
      document.addEventListener('mousemove', handleResizeMove);
      document.addEventListener('mouseup', handleResizeEnd);
    }

    return () => {
      document.removeEventListener('mousemove', handleResizeMove);
      document.removeEventListener('mouseup', handleResizeEnd);
    };
  }, [isResizing]);

  // Resize terminal when height changes
  useEffect(() => {
    if (term && isVisible && terminalRef.current) {
      // Wait a bit for the height change to be applied to the DOM
      setTimeout(() => {
        try {
          // Check if the terminal container has dimensions
          if (terminalRef.current &&
              terminalRef.current.clientWidth > 0 &&
              terminalRef.current.clientHeight > 0) {

            // Calculate dimensions based on container size
            const cols = Math.floor(terminalRef.current.clientWidth / 9);
            const rows = Math.floor(terminalRef.current.clientHeight / 17);

            if (cols > 0 && rows > 0) {
              console.log(`Resizing terminal after height change to ${cols}x${rows}`);

              // Resize the terminal directly
              term.resize(cols, rows);

              // Force a redraw of the terminal
              term.refresh(0, term.rows - 1);

              // Notify the backend of the new terminal size
              invoke('resize_terminal', { rows, cols })
                .catch(error => console.error('Error notifying backend of resize:', error));
            }
          }
        } catch (error) {
          console.error('Error resizing terminal after height change:', error);
        }
      }, 100);
    }
  }, [height, term, isVisible]);

  // Update the CSS variable for height using a ref
  useEffect(() => {
    if (terminalRef.current && terminalRef.current.parentElement) {
      const container = terminalRef.current.parentElement;
      container.style.setProperty('--terminal-height', `${height}px`);
    }
  }, [height]);

  // Focus the terminal when it becomes visible
  useEffect(() => {
    if (isVisible && term) {
      // Short delay to ensure the terminal is fully rendered
      setTimeout(() => {
        if (term) {
          term.focus();
          console.log("Terminal focused after becoming visible");
        }
      }, 100);
    }
  }, [isVisible, term]);

  if (!isVisible) return null;

  // Function to focus the terminal when the container is clicked
  const handleTerminalClick = () => {
    if (term) {
      term.focus();
      console.log("Terminal focused on click");
    }
  };

  return (
    <div className={styles.terminalContainer}>
      <div className={styles.terminalHeader}>
        <div className={styles.terminalTitle}>Terminal</div>
        <button
          type="button"
          className={styles.closeButton}
          onClick={onClose}
          aria-label="Close terminal"
        >
          ×
        </button>
      </div>
      <div
        className={styles.resizeHandle}
        onMouseDown={handleResizeStart}
      ></div>
      <div
        ref={terminalRef}
        className={styles.terminal}
        onClick={handleTerminalClick}
        tabIndex={-1} // Make it focusable
      ></div>
    </div>
  );
};

export default Terminal;
