import React, { useState } from 'react';
import styles from './ChatSessions.module.css';

interface ChatSession {
  id: string;
  title: string;
  timestamp: string;
  updatedAt: string;
  messageCount: number;
}

interface ChatSessionsProps {
  isOpen: boolean;
  onClose: () => void;
  onCreateNewChat: (title: string, preserveContext: boolean) => void;
  onSelectChat: (sessionId: string) => void;
  onDeleteChat?: (sessionId: string) => void;
  onDownloadChat?: (sessionId: string) => void;
  onShareChat?: (sessionId: string) => void;
  sessions: ChatSession[];
}

const ChatSessions: React.FC<ChatSessionsProps> = ({
  isOpen,
  onClose,
  onCreateNewChat,
  onSelectChat,
  onDeleteChat,
  onDownloadChat,
  onShareChat,
  sessions
}) => {
  const [newChatTitle, setNewChatTitle] = useState('');
  const [preserveContext, setPreserveContext] = useState(false);
  const [confirmDelete, setConfirmDelete] = useState<string | null>(null);

  const handleCreateNewChat = () => {
    onCreateNewChat(newChatTitle, preserveContext);
    setNewChatTitle('');
  };

  const handleDeleteChat = (e: React.MouseEvent, sessionId: string) => {
    e.stopPropagation(); // Prevent triggering the chat selection

    if (confirmDelete === sessionId) {
      // User confirmed deletion
      if (onDeleteChat) {
        onDeleteChat(sessionId);
      }
      setConfirmDelete(null);
    } else {
      // Ask for confirmation
      setConfirmDelete(sessionId);
      // Auto-reset confirmation after 3 seconds
      setTimeout(() => {
        setConfirmDelete(null);
      }, 3000);
    }
  };

  const handleDownloadChat = (e: React.MouseEvent, sessionId: string) => {
    e.stopPropagation(); // Prevent triggering the chat selection
    if (onDownloadChat) {
      onDownloadChat(sessionId);
    }
  };

  const handleShareChat = (e: React.MouseEvent, sessionId: string) => {
    e.stopPropagation(); // Prevent triggering the chat selection
    if (onShareChat) {
      onShareChat(sessionId);
    }
  };

  if (!isOpen) return null;

  return (
    <div className={styles.chatSessionsContainer}>
      <div className={styles.chatSessionsHeader}>
        <h2>Chat Sessions</h2>
        <button type="button" className={styles.closeButton} onClick={onClose}>×</button>
      </div>

      <div className={styles.chatSessionsList}>
        {sessions.map((session) => (
          <div
            key={session.id}
            className={styles.chatSessionItem}
            onClick={() => onSelectChat(session.id)}
          >
            <div className={styles.chatSessionTitle}>
              {session.title || `Chat ${session.timestamp}`}
            </div>
            <div className={styles.chatSessionMeta}>
              Updated: {session.updatedAt}
              <br />
              Messages: {session.messageCount}
            </div>
            <div className={styles.chatSessionActions}>
              <button
                type="button"
                className={styles.actionButton}
                title="Download"
                onClick={(e) => handleDownloadChat(e, session.id)}
              >
                <span>↓</span>
              </button>
              <button
                type="button"
                className={styles.actionButton}
                title="Share"
                onClick={(e) => handleShareChat(e, session.id)}
              >
                <span>↗</span>
              </button>
              <button
                type="button"
                className={`${styles.actionButton} ${confirmDelete === session.id ? styles.deleteConfirm : ''}`}
                title={confirmDelete === session.id ? "Click again to confirm deletion" : "Delete"}
                onClick={(e) => handleDeleteChat(e, session.id)}
              >
                <span>{confirmDelete === session.id ? "✓" : "🗑"}</span>
              </button>
            </div>
          </div>
        ))}
      </div>

      <div className={styles.newChatSection}>
        <input
          type="text"
          className={styles.newChatInput}
          placeholder="Chat title (optional)"
          value={newChatTitle}
          onChange={(e) => setNewChatTitle(e.target.value)}
        />
        <div className={styles.preserveContextOption}>
          <input
            type="checkbox"
            id="preserve-context"
            checked={preserveContext}
            onChange={(e) => setPreserveContext(e.target.checked)}
          />
          <label htmlFor="preserve-context">Preserve current context</label>
        </div>
        <button
          type="button"
          className={styles.createNewChatButton}
          onClick={handleCreateNewChat}
        >
          Create New Chat
        </button>
      </div>
    </div>
  );
};

export default ChatSessions;
