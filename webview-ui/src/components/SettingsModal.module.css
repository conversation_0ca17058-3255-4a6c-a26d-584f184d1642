.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modalContent {
  background-color: #1a1f2e;
  padding: 25px;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  width: 90%;
  max-width: 500px;
  color: #ffffff;
  border: 1px solid #2a3042;
}

.modalContent h2 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #ffffff;
  font-weight: 500;
}

.inputGroup {
  margin-bottom: 20px;
}

.inputGroup label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #ffffff;
}

.inputGroup input {
  width: 100%;
  padding: 12px;
  background-color: #2a3042;
  border: 1px solid #2a3042;
  border-radius: 6px;
  font-size: 1rem;
  color: #ffffff;
}

.buttonGroup {
  margin-top: 25px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.buttonGroup button {
  padding: 12px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
}

.saveButton {
  background-color: #6c7ee1;
  color: white;
}

.saveButton:hover {
  background-color: #5a6cd1;
}

.closeButton {
  background-color: #2a3042;
  color: white;
}

.closeButton:hover {
  background-color: #3a4052;
}

.errorMessage {
  color: #ffffff;
  background-color: rgba(255, 76, 76, 0.2);
  border: 1px solid rgba(255, 76, 76, 0.3);
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 20px;
}

.successMessage {
  color: #ffffff;
  background-color: rgba(0, 200, 83, 0.2);
  border: 1px solid rgba(0, 200, 83, 0.3);
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 20px;
}
