.terminalContainer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: var(--terminal-height, 300px);
  z-index: 1000;
  background-color: #1a1f2e;
  border-bottom: 1px solid #3a3f4e;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  animation: slideDown 0.3s ease-out;
  overflow: hidden;
}

.terminalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 10px;
  background-color: #2a2f3e;
  border-bottom: 1px solid #3a3f4e;
}

.terminalTitle {
  font-weight: bold;
  color: #f8f8f8;
}

.closeButton {
  background: none;
  border: none;
  color: #f8f8f8;
  font-size: 18px;
  cursor: pointer;
  padding: 0 5px;
}

.closeButton:hover {
  color: #ff5555;
}

.terminal {
  flex: 1;
  padding: 0;
  overflow: hidden;
  width: 100%;
  height: calc(100% - 36px); /* Subtract header height */
  background-color: #1a1f2e;
  position: relative;
}

/* Target xterm.js elements to ensure proper display */
.terminal :global(.xterm) {
  height: 100% !important;
  width: 100% !important;
}

.terminal :global(.xterm-viewport) {
  background-color: #1a1f2e !important;
  overflow-y: auto !important;
  width: 100% !important;
  height: 100% !important;
}

.terminal :global(.xterm-screen) {
  width: 100% !important;
  height: 100% !important;
}

.terminal :global(.xterm-cursor) {
  background-color: #f8f8f8 !important;
  color: #1a1f2e !important;
}

.terminal :global(.xterm-cursor-layer) {
  display: block !important;
}

.terminal :global(.xterm-rows) {
  font-family: monospace !important;
  color: #f8f8f8 !important;
}

/* Ensure text is visible */
.terminal :global(.xterm-helpers) {
  position: absolute !important;
  top: 0 !important;
}

.terminal :global(.xterm-char-measure-element) {
  visibility: hidden !important;
  position: absolute !important;
  top: 0 !important;
  left: -9999em !important;
}

/* Force text to be visible */
.terminal :global(.xterm-decoration-container),
.terminal :global(.xterm-decoration) {
  z-index: 5 !important;
}

.resizeHandle {
  height: 5px;
  width: 100%;
  background-color: #3a3f4e;
  cursor: ns-resize;
  position: absolute;
  bottom: 0;
}

@keyframes slideDown {
  from {
    transform: translateY(-100%);
  }
  to {
    transform: translateY(0);
  }
}

/* When terminal is closing */
.terminalClosing {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(-100%);
  }
}
