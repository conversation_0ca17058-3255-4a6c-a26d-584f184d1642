'use client';

import React, { useEffect, useRef, useState } from 'react';
import { invoke } from '../lib/tauri-api.ts';
import styles from './CustomTerminal.module.css';

interface CustomTerminalProps {
  isVisible: boolean;
  onClose: () => void;
}

const CustomTerminal: React.FC<CustomTerminalProps> = ({ isVisible, onClose }) => {
  const terminalRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const [output, setOutput] = useState<string[]>([]);
  const [currentInput, setCurrentInput] = useState('');
  const [isShellCreated, setIsShellCreated] = useState(false);
  const [height, setHeight] = useState(300); // Default height
  const [isResizing, setIsResizing] = useState(false);
  const resizeStartYRef = useRef(0);
  const initialHeightRef = useRef(0);
  
  // Initialize shell when component mounts
  useEffect(() => {
    if (!isVisible) return;
    
    const initializeShell = async () => {
      try {
        console.log("Creating terminal shell");
        await invoke('create_terminal_shell');
        setIsShellCreated(true);
        setOutput(prev => [...prev, 'Terminal initialized. Type commands below.']);
        console.log("Shell created successfully");
      } catch (error) {
        console.error('Error creating terminal shell:', error);
        setOutput(prev => [...prev, `Error: ${error}`]);
      }
    };
    
    if (!isShellCreated) {
      initializeShell();
    }
    
    // Focus the input when terminal becomes visible
    if (inputRef.current) {
      inputRef.current.focus();
    }
    
    // Start reading from the shell
    let isReading = true;
    let animationFrameId: number;
    
    const readFromShell = async () => {
      if (!isReading) return;
      
      try {
        const data = await invoke<string | null>('read_from_terminal');
        if (data) {
          console.log("Received data from shell:", data);
          // Split by newlines and add each line to the output
          const lines = data.split(/\r?\n/);
          setOutput(prev => [...prev, ...lines.filter(line => line.trim() !== '')]);
          
          // Scroll to bottom
          if (terminalRef.current) {
            terminalRef.current.scrollTop = terminalRef.current.scrollHeight;
          }
        }
      } catch (error) {
        console.error('Error reading from shell:', error);
      }
      
      if (isReading) {
        animationFrameId = requestAnimationFrame(readFromShell);
      }
    };
    
    readFromShell();
    
    // Clean up
    return () => {
      isReading = false;
      if (animationFrameId) {
        cancelAnimationFrame(animationFrameId);
      }
    };
  }, [isVisible, isShellCreated]);
  
  // Handle input submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!currentInput.trim()) return;
    
    // Add the command to the output
    setOutput(prev => [...prev, `$ ${currentInput}`]);
    
    try {
      // Send the command to the shell
      await invoke('write_to_terminal', { data: currentInput + '\r' });
      console.log("Command sent to shell:", currentInput);
    } catch (error) {
      console.error('Error sending command to shell:', error);
      setOutput(prev => [...prev, `Error: ${error}`]);
    }
    
    // Clear the input
    setCurrentInput('');
  };
  
  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCurrentInput(e.target.value);
  };
  
  // Handle resizing
  const handleResizeStart = (e: React.MouseEvent) => {
    setIsResizing(true);
    resizeStartYRef.current = e.clientY;
    initialHeightRef.current = height;
  };
  
  useEffect(() => {
    const handleResizeMove = (e: MouseEvent) => {
      if (!isResizing) return;
      
      const deltaY = resizeStartYRef.current - e.clientY;
      const newHeight = Math.max(150, initialHeightRef.current + deltaY);
      setHeight(newHeight);
    };
    
    const handleResizeEnd = () => {
      setIsResizing(false);
    };
    
    if (isResizing) {
      document.addEventListener('mousemove', handleResizeMove);
      document.addEventListener('mouseup', handleResizeEnd);
    }
    
    return () => {
      document.removeEventListener('mousemove', handleResizeMove);
      document.removeEventListener('mouseup', handleResizeEnd);
    };
  }, [isResizing]);
  
  // Update the CSS variable for height
  useEffect(() => {
    if (terminalRef.current && terminalRef.current.parentElement) {
      const container = terminalRef.current.parentElement;
      container.style.setProperty('--terminal-height', `${height}px`);
    }
  }, [height]);
  
  // Handle keyboard shortcut (F12) to toggle terminal
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'F12') {
        e.preventDefault();
        if (isVisible) {
          onClose();
        }
      }
    };
    
    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [isVisible, onClose]);
  
  if (!isVisible) return null;
  
  return (
    <div className={styles.terminalContainer}>
      <div className={styles.terminalHeader}>
        <div className={styles.terminalTitle}>Terminal</div>
        <button
          type="button"
          className={styles.closeButton}
          onClick={onClose}
          aria-label="Close terminal"
        >
          ×
        </button>
      </div>
      <div className={styles.resizeHandle} onMouseDown={handleResizeStart}></div>
      <div ref={terminalRef} className={styles.terminal}>
        <div className={styles.terminalOutput}>
          {output.map((line, index) => (
            <div key={index} className={styles.outputLine}>
              {line}
            </div>
          ))}
        </div>
        <form onSubmit={handleSubmit} className={styles.terminalForm}>
          <span className={styles.prompt}>$</span>
          <input
            ref={inputRef}
            type="text"
            value={currentInput}
            onChange={handleInputChange}
            className={styles.terminalInput}
            autoFocus
          />
        </form>
      </div>
    </div>
  );
};

export default CustomTerminal;
