.chatSessionsContainer {
  position: absolute;
  top: 0;
  right: 0;
  width: 300px;
  height: 100%;
  background-color: #1a1f2e;
  border-left: 1px solid #2a3042;
  display: flex;
  flex-direction: column;
  z-index: 100;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.2);
}

.chatSessionsHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #2a3042;
}

.chatSessionsHeader h2 {
  margin: 0;
  font-size: 1.2rem;
  color: #ffffff;
}

.closeButton {
  background: none;
  border: none;
  color: #ffffff;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0;
  line-height: 1;
}

.chatSessionsList {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.chatSessionItem {
  padding: 12px;
  margin-bottom: 10px;
  background-color: #2a3042;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.chatSessionItem:hover {
  background-color: #3a4052;
}

.chatSessionTitle {
  font-weight: 500;
  margin-bottom: 5px;
  color: #ffffff;
}

.chatSessionMeta {
  font-size: 0.8rem;
  color: #a0a0a0;
  margin-bottom: 8px;
}

.chatSessionActions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.actionButton {
  background: none;
  border: none;
  color: #a0a0a0;
  cursor: pointer;
  padding: 2px 5px;
  font-size: 1rem;
  border-radius: 3px;
}

.actionButton:hover {
  background-color: #3a4052;
  color: #ffffff;
}

.deleteConfirm {
  background-color: rgba(255, 76, 76, 0.3) !important;
  color: #ffffff !important;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.7;
  }
}

.newChatSection {
  padding: 15px;
  border-top: 1px solid #2a3042;
}

.newChatInput {
  width: 100%;
  padding: 10px;
  margin-bottom: 10px;
  background-color: #2a3042;
  border: 1px solid #2a3042;
  border-radius: 4px;
  color: #ffffff;
}

.preserveContextOption {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 15px;
  color: #ffffff;
}

.preserveContextOption input[type="checkbox"] {
  accent-color: #6c7ee1;
}

.createNewChatButton {
  width: 100%;
  padding: 10px;
  background-color: #6c7ee1;
  color: #ffffff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}

.createNewChatButton:hover {
  background-color: #5a6cd1;
}
