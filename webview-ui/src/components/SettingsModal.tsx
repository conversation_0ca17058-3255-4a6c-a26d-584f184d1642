import React, { useState, useEffect } from 'react';
import styles from './SettingsModal.module.css'; // We'll create this CSS module next

interface ApiKeys {
  openrouter_key?: string;
  gemini_key?: string;
  ollama_url?: string;
  lm_studio_url?: string;
}

interface SettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (keys: ApiKeys) => Promise<void>;
  initialKeys: ApiKeys;
}

const SettingsModal: React.FC<SettingsModalProps> = ({ isOpen, onClose, onSave, initialKeys }) => {
  const [openRouterKey, setOpenRouterKey] = useState('');
  const [geminiKey, setGeminiKey] = useState('');
  const [ollamaUrl, setOllamaUrl] = useState('');
  const [lmStudioUrl, setLmStudioUrl] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  useEffect(() => {
    setOpenRouterKey(initialKeys.openrouter_key || '');
    setGeminiKey(initialKeys.gemini_key || '');
    setOllamaUrl(initialKeys.ollama_url || '');
    setLmStudioUrl(initialKeys.lm_studio_url || '');
  }, [initialKeys, isOpen]); // Reset when modal is opened or initialKeys change

  if (!isOpen) {
    return null;
  }

  const handleSave = async () => {
    setIsLoading(true);
    setError(null);
    setSuccessMessage(null);
    try {
      await onSave({
        openrouter_key: openRouterKey,
        gemini_key: geminiKey,
        ollama_url: ollamaUrl,
        lm_studio_url: lmStudioUrl,
      });
      setSuccessMessage('Settings saved successfully!');
      setTimeout(() => { // Optionally close after a delay or keep open
        setSuccessMessage(null);
        // onClose(); 
      }, 2000);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save API keys.');
      console.error('Save API keys error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setError(null);
    setSuccessMessage(null);
    onClose();
  }

  return (
    <div className={styles.modalOverlay}>
      <div className={styles.modalContent}>
        <h2>API Key Settings</h2>
        {error && <p className={styles.errorMessage}>{error}</p>}
        {successMessage && <p className={styles.successMessage}>{successMessage}</p>}
        
        <div className={styles.inputGroup}>
          <label htmlFor="openrouter-key">OpenRouter API Key:</label>
          <input
            type="password"
            id="openrouter-key"
            value={openRouterKey}
            onChange={(e) => setOpenRouterKey(e.target.value)}
            disabled={isLoading}
          />
        </div>
        
        <div className={styles.inputGroup}>
          <label htmlFor="gemini-key">Google Gemini API Key:</label>
          <input
            type="password"
            id="gemini-key"
            value={geminiKey}
            onChange={(e) => setGeminiKey(e.target.value)}
            disabled={isLoading}
          />
        </div>

        <div className={styles.inputGroup}>
          <label htmlFor="ollama-url">Ollama Base URL:</label>
          <input
            type="text"
            id="ollama-url"
            value={ollamaUrl}
            placeholder="http://localhost:11434"
            onChange={(e) => setOllamaUrl(e.target.value)}
            disabled={isLoading}
          />
        </div>

        <div className={styles.inputGroup}>
          <label htmlFor="lmstudio-url">LM Studio Base URL (v1 compatible):</label>
          <input
            type="text"
            id="lmstudio-url"
            value={lmStudioUrl}
            placeholder="http://localhost:1234/v1"
            onChange={(e) => setLmStudioUrl(e.target.value)}
            disabled={isLoading}
          />
        </div>
        
        <div className={styles.buttonGroup}>
          <button onClick={handleSave} disabled={isLoading} className={styles.saveButton}>
            {isLoading ? 'Saving...' : 'Save Settings'}
          </button>
          <button onClick={handleClose} disabled={isLoading} className={styles.closeButton}>
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default SettingsModal;
