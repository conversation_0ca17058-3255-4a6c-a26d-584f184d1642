'use client';

import React, { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';

// Dynamically import the Terminal component with SSR disabled
const Terminal = dynamic(
  () => import('./Terminal'),
  { ssr: false }
);

interface DynamicTerminalProps {
  isVisible: boolean;
  onClose: () => void;
}

const DynamicTerminal: React.FC<DynamicTerminalProps> = ({ isVisible, onClose }) => {
  // Only render the Terminal component on the client side
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted) return null;

  return (
    <Terminal isVisible={isVisible} onClose={onClose} />
  );
};

export default DynamicTerminal;
