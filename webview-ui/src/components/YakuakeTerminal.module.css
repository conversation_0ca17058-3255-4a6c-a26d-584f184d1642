.yakuakeContainer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 50vh; /* Default height, can be adjusted */
  background-color: #1e1e1e; /* Match XTerm theme background */
  border-bottom: 1px solid #333;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
  z-index: 1000;
  transform: translateY(-100%);
  transition: transform 0.3s ease-in-out;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* Important for child elements */
}

.yakuakeContainer.show {
  transform: translateY(0);
}

.yakuakeContainer.hide {
  transform: translateY(-100%);
}

.terminalHeader {
  background-color: #2a2a2a;
  padding: 8px 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #d4d4d4;
  flex-shrink: 0; /* Prevent header from shrinking */
  border-bottom: 1px solid #333;
}

.terminalHeader span {
  font-weight: bold;
}

.closeButton {
  background: none;
  border: none;
  color: #d4d4d4;
  font-size: 20px;
  cursor: pointer;
  padding: 0 5px;
}

.closeButton:hover {
  color: #ff6b6b;
}

.terminalContent {
  flex-grow: 1; /* Allow content to fill available space */
  /* padding: 5px; */ /* Removed padding */
  overflow: hidden; /* Ensure xterm fits and scrolls within this */
  display: flex; /* Needed for xterm to fill height */
  flex-direction: column; /* Needed for xterm to fill height */
  position: relative; /* Establish stacking context, just in case */
}

/* Ensure xterm instance itself takes full space of its container */
.terminalContent > div { /* This targets the first div xterm creates (usually .xterm) */
  width: 100% !important;
  height: 100% !important;
  overflow: hidden !important; /* Prevent scrollbars on this immediate child */
  resize: none !important; /* Attempt to disable resize handles */
}

/* Target deeper xterm elements that might have resize handles or overflow issues */
.terminalContent :global(.xterm-viewport),
.terminalContent :global(.xterm-screen) {
  resize: none !important;
  overflow: hidden !important; /* Viewport should handle scrolling, not show its own scrollbars */
}

/* General rule for any divs deep inside that might get resize handles by mistake */
.terminalContent div div { /* This is a bit too broad, could be refined if needed */
  resize: none !important;
}

/* Specifically target the xterm helper textarea to ensure it's hidden and non-interactive */
.terminalContent :global(textarea.xterm-helper-textarea) {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 1px !important; /* Minimal size instead of 0 to ensure it's part of layout for focus */
  height: 1px !important;
  opacity: 0 !important; /* Keep it invisible */
  overflow: hidden !important;
  border: none !important;
  padding: 0 !important;
  margin: 0 !important;
  resize: none !important;
  z-index: -1 !important; /* Send it behind other xterm layers */
  pointer-events: none !important; /* It should still get events programmatically from xterm.js core */
  outline: none !important;
  background: transparent !important;
  color: transparent !important;
}

/* Ensure the screen and text layer are visible and on top of any helper textarea */
.terminalContent :global(.xterm-screen) {
  z-index: 1 !important; /* Above helper textarea */
  background-color: transparent !important; /* Make screen background transparent to see if white box is behind/separate */
}

.terminalContent :global(.xterm-text-layer) {
  z-index: 2 !important; /* Above screen background, below cursor */
  background-color: transparent !important; /* Text layer itself should be transparent */
}

.terminalContent :global(.xterm-cursor-layer) {
  z-index: 3 !important; /* Cursor on top */
}

/* Remove any accidental background from the main .xterm container if it's the white box */
.terminalContent > div { /* This targets .xterm */
  background-color: transparent !important;
}
