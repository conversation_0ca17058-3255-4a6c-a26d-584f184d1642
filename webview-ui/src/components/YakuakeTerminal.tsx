'use client';

import React, { useEffect, useRef, useState, useCallback } from 'react';
import { Terminal as XTerminal } from 'xterm';
import { FitAddon } from 'xterm-addon-fit';
import { WebLinksAddon } from 'xterm-addon-web-links';
import { invoke } from '../lib/tauri-api.ts';
import { listen as tauriListen, UnlistenFn } from '@tauri-apps/api/event';
import styles from './YakuakeTerminal.module.css'; // We'll create this CSS module

// Extend the Window interface for Tauri IPC
declare global {
  interface Window {
    __TAURI_INTERNALS__?: {
      invoke: (cmd: string, args?: Record<string, unknown>) => Promise<any>;
      // Define other properties of __TAURI_INTERNALS__ if needed by your check
    };
    __TAURI__?: object; // Add definition for the basic __TAURI__ global
  }
}

interface YakuakeTerminalProps {
  // Props to control visibility, perhaps a global keybind (e.g., F12)
  // For now, let's assume it's controlled by a parent state or a global event
  // We can add a toggle button or key listener within this component too.
  show: boolean;
  toggleShow: () => void;
}

const YakuakeTerminal: React.FC<YakuakeTerminalProps> = ({ show, toggleShow }) => {
  const terminalContainerRef = useRef<HTMLDivElement>(null);
  const xtermRef = useRef<XTerminal | null>(null);
  const fitAddonRef = useRef<FitAddon | null>(null);
  const [terminalId, setTerminalId] = useState<string | null>(null);
  // const [isVisible, setIsVisible] = useState(false); // Removed, use 'show' prop directly for CSS

  const [inputBuffer, setInputBuffer] = useState(''); // For programmatic command input
  const [pendingLlmCommand, setPendingLlmCommand] = useState<string | null>(null); // For LLM command confirmation

  // Debounce resize
  const debounce = <F extends (...args: any[]) => any>(func: F, waitFor: number) => { // eslint-disable-line @typescript-eslint/no-explicit-any
    let timeout: ReturnType<typeof setTimeout> | null = null;
    return (...args: Parameters<F>): Promise<ReturnType<F>> => // eslint-disable-line @typescript-eslint/no-explicit-any
      new Promise(resolve => {
        if (timeout) {
          clearTimeout(timeout);
        }
        timeout = setTimeout(() => resolve(func(...args)), waitFor);
      });
  };

  const fitTerminal = useCallback(() => {
    if (fitAddonRef.current) {
      try {
        fitAddonRef.current.fit();
        if (xtermRef.current && terminalId) {
          const { cols, rows } = xtermRef.current;
          console.log(`Resizing backend terminal ${terminalId} to ${cols}x${rows}`);
          invoke('yk_resize_terminal', { terminalId, cols, rows }).catch(console.error);
        }
      } catch (e) {
        console.error("Error fitting terminal:", e);
      }
    }
  }, [terminalId]); // fitTerminal depends on terminalId

  const debouncedFit = useCallback(debounce(fitTerminal, 50), [fitTerminal]); // ESLint might want fitTerminal here

  // Initialize and manage terminal instance
  useEffect(() => {
    // Initialize only once when 'show' is true for the first time and terminal isn't created
    if (show && !xtermRef.current && terminalContainerRef.current) {
      console.log('YakuakeTerminal: Initializing XTerm for the first time');
      const term = new XTerminal({
        cursorBlink: true,
        fontSize: 12, // Reduced font size
        fontFamily: 'monospace', // Keep a standard font family
        disableStdin: false,
        allowProposedApi: true,
        convertEol: true, // Usually good to have
        // Simplified theme to rule out interference, can be restored later
        theme: {
          background: '#1e1e1e',
          foreground: '#d4d4d4',
          cursor: '#d4d4d4',
        },
        rows: 20, 
      });
      const fitAddon = new FitAddon();
      // const webLinksAddon = new WebLinksAddon(); // Temporarily remove

      xtermRef.current = term;
      fitAddonRef.current = fitAddon;

      term.loadAddon(fitAddon);
      // term.loadAddon(webLinksAddon); // Temporarily remove

      // Explicitly clear the container before opening
      if (terminalContainerRef.current) {
        terminalContainerRef.current.innerHTML = '';
      }
      term.open(terminalContainerRef.current);
      term.clear(); // Clear any initial artifacts immediately after open
      debouncedFit(); // Fit the terminal early

      // Initial messages before attempting to connect to backend
      term.writeln('Welcome to Yakuake Terminal!');
      term.writeln('Attempting to connect to backend for shell session...');

      const MAX_IPC_CHECK_ATTEMPTS = 30; // Poll for up to 30 * 250ms = 7.5 seconds
      const IPC_CHECK_INTERVAL = 250;
      let ipcCheckAttempts = 0;
      let ipcReadyInterval: NodeJS.Timeout | null = null;

      const attemptPtyCreation = () => {
        if (xtermRef.current && terminalContainerRef.current) { // Check component still mounted
          console.log('Attempting PTY creation...');
          xtermRef.current.writeln('Tauri IPC ready. Creating shell session...');
          invoke<string>('yk_create_terminal')
            .then((newId: string) => {
              setTerminalId(newId);
              if (xtermRef.current) {
                xtermRef.current.writeln(`Shell session created with ID: ${newId}`);
                xtermRef.current.focus();
                debouncedFit();
              }
            })
            .catch((err: any) => { // eslint-disable-line @typescript-eslint/no-explicit-any
              console.error('Error creating Yakuake terminal:', err);
              if (xtermRef.current) {
                xtermRef.current.writeln(`Error creating shell: ${err}`);
              }
            });
        }
      };
      
      const checkIpcReady = () => {
        const tauriInternals = window.__TAURI_INTERNALS__;
        const tauriGlobal = window.__TAURI__; // Check for the simpler global too
        const readyState = document.readyState;

        console.log(`IPC Check Attempt ${ipcCheckAttempts + 1}: document.readyState=${readyState}, window.__TAURI__ is ${tauriGlobal ? 'defined' : 'undefined'}, window.__TAURI_INTERNALS__ is ${tauriInternals ? 'defined' : 'undefined'}, invoke is ${tauriInternals?.invoke ? 'defined' : 'undefined'}`);

        if (tauriInternals?.invoke) {
          console.log('Tauri IPC is ready (invoke function found).');
          if (ipcReadyInterval) clearInterval(ipcReadyInterval);
          attemptPtyCreation();
        } else {
          ipcCheckAttempts++;
          // console.log(`Tauri IPC not ready, attempt ${ipcCheckAttempts}/${MAX_IPC_CHECK_ATTEMPTS}. Retrying in ${IPC_CHECK_INTERVAL}ms...`); // Redundant with above log
          if (ipcCheckAttempts >= MAX_IPC_CHECK_ATTEMPTS) {
            if (ipcReadyInterval) clearInterval(ipcReadyInterval);
            console.error("Tauri IPC (invoke function) not available after multiple attempts.");
            if (xtermRef.current) {
              xtermRef.current.writeln("Error: Tauri IPC timed out. Cannot create shell.");
            }
          }
        }
      };

      // Start polling after a brief initial delay
      setTimeout(() => {
        // Initial check
        if (window.__TAURI_INTERNALS__?.invoke) {
          checkIpcReady();
        } else {
          // Start polling if not immediately available
          ipcReadyInterval = setInterval(checkIpcReady, IPC_CHECK_INTERVAL);
        }
      }, 100);


      // Old invoke logic (now inside attemptPtyCreation)
      // invoke<string>('yk_create_terminal')
      //   .then((newId: string) => {
      //     setTerminalId(newId);
      //     term.writeln(`Shell session created with ID: ${newId}`);
          // term.focus(); // Moved into .then()
          // debouncedFit(); // Moved into .then()
        // })
        // .catch((err: any) => { // eslint-disable-line @typescript-eslint/no-explicit-any
        //   console.error('Error creating Yakuake terminal:', err);
        //   term.writeln(`Error creating shell: ${err}`);
        // });

      term.onData((data: string) => {
        console.log('Key pressed in Xterm (onData event):', data); // Debug log
        if (terminalId) {
          invoke('yk_write_to_terminal', { terminalId, data }).catch(console.error);
        }
      });
       window.addEventListener('resize', debouncedFit); // Keep resize listener
    }
    // No specific action needed here when 'show' becomes false, CSS handles hiding.
    // The Xterm instance persists.

    // Cleanup for the resize listener if the component itself unmounts.
    // The Xterm instance cleanup (dispose) should happen if the entire YakuakeTerminal is unmounted from DOM,
    // not just hidden. For now, we assume it stays mounted and is only hidden by CSS.
    // If true unmount/dispose is needed, a separate cleanup effect for xtermRef.current would be better.
    return () => {
       window.removeEventListener('resize', debouncedFit);
    };
  }, [show, debouncedFit]); 

  // Effect for handling backend events
  useEffect(() => {
    if (!terminalId || !xtermRef.current) return;

    console.log(`Setting up listeners for terminal ID: ${terminalId}`); // Debug log
    const listeners: Array<Promise<UnlistenFn>> = [];
    const term = xtermRef.current;

    listeners.push(
      tauriListen(`yk-terminal-data-${terminalId}`, event => {
        if (typeof event.payload === 'string') {
          term.write(event.payload);
        }
      })
    );

    listeners.push(
      tauriListen(`yk-terminal-closed-${terminalId}`, () => {
        term.writeln('\r\nShell session closed.');
        // Potentially nullify terminalId or offer to restart
      })
    );
    
    listeners.push(
      tauriListen(`yk-terminal-error-${terminalId}`, event => {
        if (typeof event.payload === 'string') {
          term.writeln(`\r\nError: ${event.payload}`);
        }
      })
    );

    return () => {
      console.log(`Cleaning up listeners for terminal ID: ${terminalId}`); // Debug log
      Promise.all(listeners).then(unlisteners => unlisteners.forEach(unlisten => unlisten()));
    };
  }, [terminalId, xtermRef]); // Added terminalId and xtermRef (as term is derived from it)

  // Effect for global keybind (e.g., F12) to toggle visibility
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'F12') { // Or any other preferred key
        event.preventDefault();
        toggleShow();
      }
    };
    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [toggleShow]);

  // Focus terminal when it becomes visible and fit it
   useEffect(() => {
    if (show && xtermRef.current && terminalContainerRef.current) {
      // Ensure the terminal div is actually visible in the DOM before focusing/fitting
      // This might require waiting for CSS transitions if any were causing issues.
      // A small delay can help ensure dimensions are stable after CSS transition.
      const focusAndFitWithDelay = () => {
        if (xtermRef.current && terminalContainerRef.current && terminalContainerRef.current.offsetHeight > 0) {
          console.log('YakuakeTerminal: Focusing and fitting on show.');
          xtermRef.current.focus();
          debouncedFit();
        } else if (xtermRef.current) { // if container not ready, try again shortly
            console.warn('YakuakeTerminal: Container not ready for focus/fit, retrying shortly.');
            setTimeout(focusAndFitWithDelay, 100);
        }
      };
      setTimeout(focusAndFitWithDelay, 50); // Initial delay for CSS transition
    }
  }, [show, debouncedFit]); // Removed isVisible, as 'show' prop is the source of truth


  // Function to send a command programmatically
  const sendCommand = (command: string) => {
    if (terminalId && xtermRef.current) {
          invoke('yk_send_command_to_shell', { terminalId, command })
        .then(() => {
          xtermRef.current?.writeln(`> ${command}`); // Echo command locally for now
          setInputBuffer(''); // Clear buffer
        })
        .catch((err: any) => { // eslint-disable-line @typescript-eslint/no-explicit-any
          console.error('Error sending command programmatically:', err);
          xtermRef.current?.writeln(`Error sending command: ${err}`);
        });
    } else {
      xtermRef.current?.writeln('Terminal not ready to send command.');
    }
  };

  // Example of how to expose sendCommand or use it with a UI element
  // This is a placeholder for where the LLM would interact
  const handleInitiateLlmCommand = () => {
    if (inputBuffer.trim()) {
      setPendingLlmCommand(inputBuffer.trim());
      // setInputBuffer(''); // Clear input buffer after initiating confirmation
    }
  };

  const handleConfirmLlmCommand = () => {
    if (pendingLlmCommand) {
      sendCommand(pendingLlmCommand);
      setPendingLlmCommand(null);
      setInputBuffer(''); // Clear input buffer after sending
    }
  };

  const handleCancelLlmCommand = () => {
    setPendingLlmCommand(null);
    // Optionally clear inputBuffer or leave it if user wants to edit
    // setInputBuffer('');
  };

  // if (!isVisible && !show) return null; // Component is always rendered, CSS handles visibility

  const handleTerminalDivClick = () => {
    if (xtermRef.current) {
      xtermRef.current.focus();
    }
  };

  return (
    <div className={`${styles.yakuakeContainer} ${show ? styles.show : styles.hide}`}>
      <div className={styles.terminalHeader}>
        <span>Yakuake Terminal (ID: {terminalId || 'N/A'})</span>
        <div style={{ marginLeft: 'auto', display: 'flex', alignItems: 'center' }}>
          {pendingLlmCommand ? (
            <>
              <span style={{ marginRight: '10px', color: '#yellow' }}>Confirm: </span>
              <code style={{ marginRight: '10px', backgroundColor: '#333', padding: '2px 5px', borderRadius: '3px' }}>
                {pendingLlmCommand}
              </code>
              <button onClick={handleConfirmLlmCommand} style={{ marginRight: '5px', color: 'lime' }}>Confirm</button>
              <button onClick={handleCancelLlmCommand} style={{ color: 'red' }}>Cancel</button>
            </>
          ) : (
            <>
              <input 
                type="text" 
                value={inputBuffer} 
                onChange={(e) => setInputBuffer(e.target.value)}
                placeholder="LLM Suggested Command"
                style={{ marginRight: '10px', minWidth: '200px' }}
                onKeyDown={(e) => { if (e.key === 'Enter') handleInitiateLlmCommand(); }}
              />
              <button onClick={handleInitiateLlmCommand}>Queue LLM Cmd</button>
            </>
          )}
          <button onClick={toggleShow} className={styles.closeButton} style={{ marginLeft: '10px' }}>×</button>
        </div>
      </div>
      <div 
        ref={terminalContainerRef} 
        className={styles.terminalContent}
        onClick={handleTerminalDivClick} // Ensure focus on click
        tabIndex={0} // Make it focusable
      ></div>
    </div>
  );
};

export default YakuakeTerminal;
