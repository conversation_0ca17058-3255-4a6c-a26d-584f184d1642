.terminalContainer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: var(--terminal-height, 300px);
  z-index: 1000;
  background-color: #1a1f2e;
  border-bottom: 1px solid #3a3f4e;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  animation: slideDown 0.3s ease-out;
  overflow: hidden;
}

.terminalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 10px;
  background-color: #2a2f3e;
  border-bottom: 1px solid #3a3f4e;
}

.terminalTitle {
  font-weight: bold;
  color: #f8f8f8;
}

.closeButton {
  background: none;
  border: none;
  color: #f8f8f8;
  font-size: 18px;
  cursor: pointer;
  padding: 0 5px;
}

.closeButton:hover {
  color: #ff5555;
}

.terminal {
  flex: 1;
  padding: 10px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background-color: #1a1f2e;
  color: #f8f8f8;
  font-family: monospace;
  font-size: 14px;
  line-height: 1.5;
}

.terminalOutput {
  flex: 1;
  overflow-y: auto;
  padding-bottom: 10px;
  white-space: pre-wrap;
  word-break: break-word;
}

.outputLine {
  margin: 0;
  padding: 0;
}

.terminalForm {
  display: flex;
  align-items: center;
  padding: 5px 0;
  border-top: 1px solid #3a3f4e;
}

.prompt {
  color: #98c379;
  margin-right: 8px;
}

.terminalInput {
  flex: 1;
  background: transparent;
  border: none;
  color: #f8f8f8;
  font-family: monospace;
  font-size: 14px;
  outline: none;
  padding: 0;
}

.resizeHandle {
  height: 5px;
  width: 100%;
  background-color: #3a3f4e;
  cursor: ns-resize;
  position: absolute;
  bottom: 0;
}

@keyframes slideDown {
  from {
    transform: translateY(-100%);
  }
  to {
    transform: translateY(0);
  }
}

/* When terminal is closing */
.terminalClosing {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(-100%);
  }
}
