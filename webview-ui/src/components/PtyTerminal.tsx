'use client';

import React, { useEffect, useRef, useState } from 'react';
import styles from './Terminal.module.css';

// Types for dynamic imports
type Terminal = any;
type FitAddon = any;
type IPty = any;

// Debounce function implementation
function debounce(func: Function, wait: number) {
  let timeout: NodeJS.Timeout | null = null;
  return function(...args: any[]) {
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

interface TerminalProps {
  isVisible: boolean;
  onClose: () => void;
}

const PtyTerminal: React.FC<TerminalProps> = ({ isVisible, onClose }) => {
  const terminalRef = useRef<HTMLDivElement>(null);
  const [term, setTerm] = useState<Terminal | null>(null);
  const [ptyProcess, setPtyProcess] = useState<IPty | null>(null);
  const [fitAddon, setFitAddon] = useState<FitAddon | null>(null);
  const [isResizing, setIsResizing] = useState(false);
  const [height, setHeight] = useState(300); // Default height
  const resizeStartYRef = useRef(0);
  const initialHeightRef = useRef(0);

  // Initialize terminal
  useEffect(() => {
    // Only run in browser environment
    if (typeof window === 'undefined') return;

    if (!terminalRef.current || !isVisible) return;

    console.log("Initializing PTY terminal...");

    // Clean up any existing terminal
    if (term) {
      term.dispose();
      setTerm(null);
      setFitAddon(null);
    }

    // Clean up any existing pty process
    if (ptyProcess) {
      try {
        ptyProcess.kill();
      } catch (error) {
        console.error('Error killing PTY process:', error);
      }
      setPtyProcess(null);
    }

    let isComponentMounted = true;

    const initTerminal = async () => {
      try {
        // Dynamically import the required modules
        const [
          xtermModule,
          fitAddonModule,
          webLinksAddonModule,
          tauriPtyModule
        ] = await Promise.all([
          import('xterm'),
          import('xterm-addon-fit'),
          import('xterm-addon-web-links'),
          import('tauri-pty')
        ]);

        // Import CSS
        await import('xterm/css/xterm.css');

        const XTerm = xtermModule.Terminal;
        const FitAddon = fitAddonModule.FitAddon;
        const WebLinksAddon = webLinksAddonModule.WebLinksAddon;
        const { spawn } = tauriPtyModule;

        // Create terminal instance
        const newTerm = new XTerm({
          fontFamily: 'monospace',
          fontSize: 14,
          theme: {
            background: '#1a1f2e',
            foreground: '#f8f8f8',
            cursor: '#f8f8f8',
            black: '#000000',
            red: '#e06c75',
            green: '#98c379',
            yellow: '#e5c07b',
            blue: '#61afef',
            magenta: '#c678dd',
            cyan: '#56b6c2',
            white: '#dcdfe4',
            brightBlack: '#5c6370',
            brightRed: '#e06c75',
            brightGreen: '#98c379',
            brightYellow: '#e5c07b',
            brightBlue: '#61afef',
            brightMagenta: '#c678dd',
            brightCyan: '#56b6c2',
            brightWhite: '#ffffff',
          },
          cursorBlink: true,
          scrollback: 1000,
          convertEol: true,
        });

        // Create and load addons
        const newFitAddon = new FitAddon();
        newTerm.loadAddon(newFitAddon);
        newTerm.loadAddon(new WebLinksAddon());

        // Open terminal in the container
        if (terminalRef.current && isComponentMounted) {
          console.log("Opening terminal in container");

          // Clear the terminal container first
          terminalRef.current.innerHTML = '';

          // Open the terminal
          newTerm.open(terminalRef.current);

          // Set state
          setTerm(newTerm);
          setFitAddon(newFitAddon);

          // Fit the terminal to its container
          setTimeout(() => {
            if (newFitAddon && isComponentMounted) {
              try {
                newFitAddon.fit();
                console.log("Terminal fitted to container");
              } catch (error) {
                console.error('Error fitting terminal:', error);
              }
            }
          }, 100);

          // Determine which shell to use based on the platform
          const platform = navigator.platform.toLowerCase();
          let shell = '';
          let args: string[] = [];

          if (platform.includes('win')) {
            shell = 'powershell.exe';
          } else if (platform.includes('mac')) {
            shell = '/bin/zsh';
            args = ['-l']; // Login shell
          } else {
            shell = '/bin/bash';
            args = ['-l']; // Login shell
          }

          console.log(`Spawning shell: ${shell}`);

          // Spawn the PTY process
          try {
            const pty = spawn(shell, args, {
              cols: newTerm.cols,
              rows: newTerm.rows,
            });

            console.log("PTY process spawned");
            setPtyProcess(pty);

            // Set up data handlers
            pty.onData((data: string) => {
              if (newTerm && isComponentMounted) {
                newTerm.write(data);
              }
            });

            newTerm.onData((data: string) => {
              if (pty && isComponentMounted) {
                pty.write(data);
              }
            });

            // Handle terminal resize
            newTerm.onResize(({ cols, rows }: { cols: number, rows: number }) => {
              if (pty && isComponentMounted) {
                try {
                  pty.resize(cols, rows);
                  console.log(`Terminal resized to ${cols}x${rows}`);
                } catch (error) {
                  console.error('Error resizing PTY:', error);
                }
              }
            });

            // Focus the terminal
            setTimeout(() => {
              if (newTerm && isComponentMounted) {
                newTerm.focus();
                console.log("Terminal focused");
              }
            }, 100);
          } catch (error) {
            console.error('Error spawning PTY process:', error);
            newTerm.writeln('\r\nError: Failed to start terminal process.');
            newTerm.writeln(`\r\n${error}`);
          }
        }
      } catch (error) {
        console.error('Error initializing terminal:', error);
      }
    };

    initTerminal();

    // Clean up on unmount or when terminal is hidden
    return () => {
      console.log("Cleaning up terminal");
      isComponentMounted = false;

      if (term) {
        term.dispose();
        setTerm(null);
      }

      if (ptyProcess) {
        try {
          ptyProcess.kill();
          console.log("PTY process killed");
        } catch (error) {
          console.error('Error killing PTY process:', error);
        }
        setPtyProcess(null);
      }

      setFitAddon(null);
    };
  }, [isVisible]);

  // Handle window resize
  useEffect(() => {
    // Only run in browser environment
    if (typeof window === 'undefined') return;

    if (!fitAddon || !isVisible) return;

    const handleResize = debounce(() => {
      if (fitAddon) {
        try {
          fitAddon.fit();
          console.log("Terminal fitted after window resize");
        } catch (error) {
          console.error('Error fitting terminal after resize:', error);
        }
      }
    }, 100);

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [fitAddon, isVisible]);

  // Handle keyboard shortcut (F12) to toggle terminal
  useEffect(() => {
    // Only run in browser environment
    if (typeof window === 'undefined') return;

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'F12') {
        e.preventDefault();
        if (isVisible) {
          onClose();
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [isVisible, onClose]);

  // Handle resizing
  const handleResizeStart = (e: React.MouseEvent) => {
    setIsResizing(true);
    resizeStartYRef.current = e.clientY;
    initialHeightRef.current = height;
  };

  useEffect(() => {
    // Only run in browser environment
    if (typeof window === 'undefined') return;

    const handleResizeMove = (e: MouseEvent) => {
      if (!isResizing) return;

      const deltaY = resizeStartYRef.current - e.clientY;
      const newHeight = Math.max(150, initialHeightRef.current + deltaY);
      setHeight(newHeight);

      // Fit terminal to new size
      if (fitAddon) {
        try {
          fitAddon.fit();
        } catch (error) {
          console.error('Error fitting terminal during resize:', error);
        }
      }
    };

    const handleResizeEnd = () => {
      setIsResizing(false);
    };

    if (isResizing) {
      document.addEventListener('mousemove', handleResizeMove);
      document.addEventListener('mouseup', handleResizeEnd);
    }

    return () => {
      document.removeEventListener('mousemove', handleResizeMove);
      document.removeEventListener('mouseup', handleResizeEnd);
    };
  }, [isResizing, fitAddon]);

  // Update the CSS variable for height using a ref
  useEffect(() => {
    // Only run in browser environment
    if (typeof window === 'undefined') return;

    if (terminalRef.current && terminalRef.current.parentElement) {
      const container = terminalRef.current.parentElement;
      container.style.setProperty('--terminal-height', `${height}px`);
    }
  }, [height]);

  if (!isVisible) return null;

  return (
    <div className={styles.terminalContainer}>
      <div className={styles.terminalHeader}>
        <div className={styles.terminalTitle}>Terminal</div>
        <button
          type="button"
          className={styles.closeButton}
          onClick={onClose}
          aria-label="Close terminal"
        >
          ×
        </button>
      </div>
      <div
        className={styles.resizeHandle}
        onMouseDown={handleResizeStart}
      ></div>
      <div
        ref={terminalRef}
        className={styles.terminal}
        tabIndex={-1} // Make it focusable
      ></div>
    </div>
  );
};

export default PtyTerminal;
