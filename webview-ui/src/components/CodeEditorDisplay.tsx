'use client';

import React, { useRef, useImperativeHandle, forwardRef } from 'react';
import { Editor, Monaco, OnMount } from '@monaco-editor/react'; // Removed OnChange
import type * as monacoEditor from 'monaco-editor';

export interface CodeEditorDisplayHandles {
  getSelectedText: () => string | null;
}

interface CodeEditorDisplayProps {
  code: string;
  language: string;
  height?: string; 
  onCodeChange?: (value: string | undefined) => void;
}

const CodeEditorDisplay = forwardRef<CodeEditorDisplayHandles, CodeEditorDisplayProps>(
  ({ code, language, height = "400px", onCodeChange }, ref) => {
    const editorRef = useRef<monacoEditor.editor.IStandaloneCodeEditor | null>(null);

    const editorOptions: monacoEditor.editor.IStandaloneEditorConstructionOptions = {
      readOnly: !onCodeChange,
      minimap: { enabled: true },
      scrollBeyondLastLine: false,
      automaticLayout: true,
      wordWrap: "on",
      // theme: 'vs-dark', // Default theme, can be configured
    };

    const handleEditorDidMount: OnMount = (editor, _monaco: Monaco) => {
      editorRef.current = editor;
      // You can get access to the editor instance here
      // editor.focus();
    };
    
    useImperativeHandle(ref, () => ({
      getSelectedText: () => {
        if (editorRef.current) {
          const selection = editorRef.current.getSelection();
          if (selection && !selection.isEmpty()) {
            return editorRef.current.getModel()?.getValueInRange(selection) || null;
          }
        }
        return null;
      },
    }));

    const loadingFallback = <div>Loading editor...</div>;

    return (
      <div style={{ border: '1px solid var(--vscode-editorWidget-border, #454545)', borderRadius: '4px', overflow: 'hidden' }}>
        <Editor
          height={height}
          language={language.toLowerCase()}
          value={code}
          options={editorOptions}
          onMount={handleEditorDidMount}
          onChange={onCodeChange}
          loading={loadingFallback}
        />
      </div>
    );
  }
);

CodeEditorDisplay.displayName = 'CodeEditorDisplay'; // Setting displayName for forwardRef component

export default CodeEditorDisplay;
