'use client';

import React, { useEffect, useRef, useState } from 'react';
import { invoke } from '../lib/tauri-api';
import { terminalService } from '../services/TerminalService';
import styles from './Terminal.module.css';

// Simple debounce function implementation
function debounce(func: Function, wait: number) {
  let timeout: NodeJS.Timeout | null = null;
  return function(...args: any[]) {
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// Type definitions for xterm.js
type XTerm = any;
type FitAddon = any;

interface TerminalProps {
  isVisible: boolean;
  onClose: () => void;
}

const NodeTerminal: React.FC<TerminalProps> = ({ isVisible, onClose }) => {
  const terminalRef = useRef<HTMLDivElement>(null);
  const [term, setTerm] = useState<XTerm | null>(null);
  const [fitAddon, setFitAddon] = useState<FitAddon | null>(null);
  const [isResizing, setIsResizing] = useState(false);
  const [height, setHeight] = useState(300); // Default height
  const resizeStartYRef = useRef(0);
  const initialHeightRef = useRef(0);

  // Initialize terminal
  useEffect(() => {
    if (!terminalRef.current || !isVisible) return;

    console.log("Initializing terminal...");

    // Clean up any existing terminal
    if (term) {
      term.dispose();
      setTerm(null);
      setFitAddon(null);
    }

    let newTerm: XTerm | null = null;
    let newFitAddon: FitAddon | null = null;
    let isComponentMounted = true;

    // Dynamically import xterm.js and addons
    const initTerminal = async () => {
      try {
        // Dynamically import the CSS
        await import('xterm/css/xterm.css');

        // Dynamically import the modules
        const xtermModule = await import('xterm');
        const fitAddonModule = await import('xterm-addon-fit');
        const webLinksAddonModule = await import('xterm-addon-web-links');

        const XTerm = xtermModule.Terminal;
        const FitAddon = fitAddonModule.FitAddon;
        const WebLinksAddon = webLinksAddonModule.WebLinksAddon;

        // Create terminal instance with simplified settings
        newTerm = new XTerm({
          fontFamily: 'monospace',
          fontSize: 14,
          theme: {
            background: '#1a1f2e',
            foreground: '#f8f8f8',
            cursor: '#f8f8f8',
            black: '#000000',
            red: '#e06c75',
            green: '#98c379',
            yellow: '#e5c07b',
            blue: '#61afef',
            magenta: '#c678dd',
            cyan: '#56b6c2',
            white: '#dcdfe4',
            brightBlack: '#5c6370',
            brightRed: '#e06c75',
            brightGreen: '#98c379',
            brightYellow: '#e5c07b',
            brightBlue: '#61afef',
            brightMagenta: '#c678dd',
            brightCyan: '#56b6c2',
            brightWhite: '#ffffff',
          },
          cursorBlink: true,
          scrollback: 1000,
          convertEol: true,
          disableStdin: false,
          allowProposedApi: true,
          allowTransparency: false,
          rendererType: 'canvas',
        });

        // Create and load addons
        newFitAddon = new FitAddon();
        newTerm.loadAddon(newFitAddon);
        newTerm.loadAddon(new WebLinksAddon());

        // Open terminal in the container
        if (terminalRef.current && isComponentMounted) {
          console.log("Opening terminal in container");

          // Clear the terminal container first
          terminalRef.current.innerHTML = '';

          // Open the terminal
          newTerm.open(terminalRef.current);

          // Write a test message to ensure the terminal is working
          newTerm.writeln('Terminal initialized...');

          // Set state
          setTerm(newTerm);
          setFitAddon(newFitAddon);

          // Initialize the terminal service
          try {
            console.log("Initializing terminal service");

            // Add a test message to verify the terminal is responsive
            newTerm.writeln('Testing terminal input/output...');

            // Add a handler to log all terminal input
            newTerm.onData((data: string) => {
              console.log('Terminal input received:', JSON.stringify(data));
            });

            // Initialize the terminal service
            await terminalService.initialize(newTerm);
            console.log("Terminal service initialized successfully");

            // Write another test message after initialization
            newTerm.writeln('Terminal service initialized. You should be able to type now.');

            // Force focus on the terminal
            setTimeout(() => {
              newTerm.focus();
              console.log("Terminal focused after initialization");
            }, 500);
          } catch (error) {
            console.error('Error initializing terminal service:', error);
            newTerm.writeln(`\r\nError initializing terminal: ${error}\r\n`);
          }

          // Wait for the terminal to be fully initialized before fitting
          setTimeout(() => {
            if (newFitAddon && isComponentMounted && newTerm && terminalRef.current) {
              try {
                // Make sure the terminal container has dimensions
                if (terminalRef.current.clientWidth > 0 && terminalRef.current.clientHeight > 0) {
                  // Set explicit dimensions first to avoid the error
                  const cols = Math.floor(terminalRef.current.clientWidth / 9); // Approximate character width
                  const rows = Math.floor(terminalRef.current.clientHeight / 17); // Approximate character height

                  // Set dimensions explicitly first
                  if (cols > 0 && rows > 0) {
                    newTerm.resize(cols, rows);
                    console.log(`Terminal resized to ${cols}x${rows}`);

                    // Notify the terminal service of the new size
                    terminalService.resizeTerminal(cols, rows)
                      .then(() => console.log(`Terminal service notified of resize to ${cols}x${rows}`))
                      .catch(error => console.error('Error notifying terminal service of resize:', error));

                    // Focus the terminal
                    newTerm.focus();
                    console.log("Terminal focused");
                  }
                } else {
                  console.warn("Terminal container has no dimensions yet");
                }
              } catch (e) {
                console.error("Error initializing terminal dimensions:", e);
              }
            }
          }, 300);
        }
      } catch (error) {
        console.error('Error initializing terminal:', error);
      }
    };

    initTerminal();

    // Clean up on unmount or when terminal is hidden
    return () => {
      console.log("Cleaning up terminal");
      isComponentMounted = false;

      // Dispose the terminal service
      terminalService.dispose().catch(error => {
        console.error('Error disposing terminal service:', error);
      });

      if (newTerm) {
        newTerm.dispose();
      }

      setTerm(null);
      setFitAddon(null);
    };
  }, [isVisible]);

  // Handle terminal resize
  useEffect(() => {
    if (!term || !isVisible || !terminalRef.current) return;

    console.log("Setting up terminal resize handler");

    // Safe resize handler that avoids the dimensions error
    const safeResize = debounce(() => {
      try {
        if (terminalRef.current && terminalRef.current.clientWidth > 0 && terminalRef.current.clientHeight > 0) {
          // Calculate dimensions based on container size
          const cols = Math.floor(terminalRef.current.clientWidth / 9); // Approximate character width
          const rows = Math.floor(terminalRef.current.clientHeight / 17); // Approximate character height

          if (cols > 0 && rows > 0) {
            console.log(`Resizing terminal to ${cols}x${rows}`);

            // Resize the terminal directly instead of using fitAddon
            term.resize(cols, rows);

            // Notify the terminal service of the new size
            terminalService.resizeTerminal(cols, rows)
              .then(() => console.log(`Terminal service notified of resize to ${cols}x${rows}`))
              .catch(error => console.error('Error notifying terminal service of resize:', error));
          }
        }
      } catch (error) {
        console.error('Error resizing terminal:', error);
      }
    }, 100);

    // Initial resize after a delay
    setTimeout(safeResize, 300);

    // Listen for window resize
    window.addEventListener('resize', safeResize);

    // Clean up
    return () => {
      console.log("Removing terminal resize handler");
      window.removeEventListener('resize', safeResize);
    };
  }, [term, isVisible]);

  // Handle keyboard shortcut (F12) to toggle terminal
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'F12') {
        e.preventDefault();
        if (isVisible) {
          onClose();
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [isVisible, onClose]);

  // Handle resizing
  const handleResizeStart = (e: React.MouseEvent) => {
    setIsResizing(true);
    resizeStartYRef.current = e.clientY;
    initialHeightRef.current = height;
  };

  useEffect(() => {
    const handleResizeMove = (e: MouseEvent) => {
      if (!isResizing) return;

      const deltaY = resizeStartYRef.current - e.clientY;
      const newHeight = Math.max(150, initialHeightRef.current + deltaY);
      setHeight(newHeight);
    };

    const handleResizeEnd = () => {
      setIsResizing(false);
    };

    if (isResizing) {
      document.addEventListener('mousemove', handleResizeMove);
      document.addEventListener('mouseup', handleResizeEnd);
    }

    return () => {
      document.removeEventListener('mousemove', handleResizeMove);
      document.removeEventListener('mouseup', handleResizeEnd);
    };
  }, [isResizing]);

  // Update the CSS variable for height using a ref
  useEffect(() => {
    if (terminalRef.current && terminalRef.current.parentElement) {
      const container = terminalRef.current.parentElement;
      container.style.setProperty('--terminal-height', `${height}px`);
    }
  }, [height]);

  // Focus the terminal when it becomes visible
  useEffect(() => {
    if (isVisible && term) {
      // Short delay to ensure the terminal is fully rendered
      setTimeout(() => {
        if (term) {
          term.focus();
          console.log("Terminal focused after becoming visible");
        }
      }, 100);
    }
  }, [isVisible, term]);

  if (!isVisible) return null;

  // Function to focus the terminal when the container is clicked
  const handleTerminalClick = () => {
    if (term) {
      term.focus();
      console.log("Terminal focused on click");
    }
  };

  return (
    <div className={styles.terminalContainer}>
      <div className={styles.terminalHeader}>
        <div className={styles.terminalTitle}>Terminal</div>
        <button
          type="button"
          className={styles.closeButton}
          onClick={onClose}
          aria-label="Close terminal"
        >
          ×
        </button>
      </div>
      <div
        className={styles.resizeHandle}
        onMouseDown={handleResizeStart}
      ></div>
      <div
        ref={terminalRef}
        className={styles.terminal}
        onClick={handleTerminalClick}
        tabIndex={-1} // Make it focusable
      ></div>
    </div>
  );
};

export default NodeTerminal;
