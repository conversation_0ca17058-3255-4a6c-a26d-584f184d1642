'use client';

import React, { useEffect, useRef, useState } from 'react';
import styles from './Terminal.module.css';

// Simple debounce function implementation
function debounce(func: Function, wait: number) {
  let timeout: NodeJS.Timeout | null = null;
  return function(...args: any[]) {
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// Type definitions for xterm.js
type XTerm = any;
type FitAddon = any;

interface TerminalProps {
  isVisible: boolean;
  onClose: () => void;
}

const SimpleTerminal: React.FC<TerminalProps> = ({ isVisible, onClose }) => {
  const terminalRef = useRef<HTMLDivElement>(null);
  const [term, setTerm] = useState<XTerm | null>(null);
  const [fitAddon, setFitAddon] = useState<FitAddon | null>(null);
  const [isResizing, setIsResizing] = useState(false);
  const [height, setHeight] = useState(300); // Default height
  const resizeStartYRef = useRef(0);
  const initialHeightRef = useRef(0);
  const [input, setInput] = useState('');
  const [history, setHistory] = useState<string[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);

  // Initialize terminal
  useEffect(() => {
    if (!terminalRef.current || !isVisible) return;

    console.log("Initializing simple terminal...");

    // Clean up any existing terminal
    if (term) {
      term.dispose();
      setTerm(null);
      setFitAddon(null);
    }

    let newTerm: XTerm | null = null;
    let newFitAddon: FitAddon | null = null;
    let isComponentMounted = true;

    // Dynamically import xterm.js and addons
    const initTerminal = async () => {
      try {
        // Dynamically import the CSS
        await import('xterm/css/xterm.css');

        // Dynamically import the modules
        const xtermModule = await import('xterm');
        const fitAddonModule = await import('xterm-addon-fit');
        const webLinksAddonModule = await import('xterm-addon-web-links');

        const XTerm = xtermModule.Terminal;
        const FitAddon = fitAddonModule.FitAddon;
        const WebLinksAddon = webLinksAddonModule.WebLinksAddon;

        // Create terminal instance with simplified settings
        newTerm = new XTerm({
          fontFamily: 'monospace',
          fontSize: 14,
          theme: {
            background: '#1a1f2e',
            foreground: '#f8f8f8',
            cursor: '#f8f8f8',
            black: '#000000',
            red: '#e06c75',
            green: '#98c379',
            yellow: '#e5c07b',
            blue: '#61afef',
            magenta: '#c678dd',
            cyan: '#56b6c2',
            white: '#dcdfe4',
            brightBlack: '#5c6370',
            brightRed: '#e06c75',
            brightGreen: '#98c379',
            brightYellow: '#e5c07b',
            brightBlue: '#61afef',
            brightMagenta: '#c678dd',
            brightCyan: '#56b6c2',
            brightWhite: '#ffffff',
          },
          cursorBlink: true,
          scrollback: 1000,
          convertEol: true,
          disableStdin: false,
          allowProposedApi: true,
          allowTransparency: false,
          rendererType: 'canvas',
        });

        // Create and load addons
        newFitAddon = new FitAddon();
        newTerm.loadAddon(newFitAddon);
        newTerm.loadAddon(new WebLinksAddon());

        // Open terminal in the container
        if (terminalRef.current && isComponentMounted) {
          console.log("Opening terminal in container");

          // Clear the terminal container first
          terminalRef.current.innerHTML = '';

          // Open the terminal
          newTerm.open(terminalRef.current);

          // Write a welcome message
          newTerm.writeln('Simple Terminal - Type commands below');
          newTerm.writeln('This is a basic terminal emulator without a real shell');
          newTerm.writeln('Type "help" for available commands');
          newTerm.writeln('');
          newTerm.write('$ ');

          // Set state
          setTerm(newTerm);
          setFitAddon(newFitAddon);

          // Set up key event handler
          newTerm.onKey(e => {
            const ev = e.domEvent;
            const printable = !ev.altKey && !ev.ctrlKey && !ev.metaKey;

            if (ev.keyCode === 13) { // Enter
              // Process the command
              const command = input;
              setInput('');
              
              // Add to history
              if (command.trim()) {
                setHistory(prev => [...prev, command]);
                setHistoryIndex(-1);
              }
              
              // Echo the command
              newTerm.writeln('');
              
              // Process the command
              processCommand(command, newTerm);
              
              // Show prompt
              newTerm.write('$ ');
            } else if (ev.keyCode === 8) { // Backspace
              // Do not delete the prompt
              if (input.length > 0) {
                setInput(prev => prev.slice(0, -1));
                newTerm.write('\b \b');
              }
            } else if (ev.keyCode === 38) { // Up arrow
              // Navigate history
              if (history.length > 0 && historyIndex < history.length - 1) {
                const newIndex = historyIndex + 1;
                setHistoryIndex(newIndex);
                const historyCommand = history[history.length - 1 - newIndex];
                
                // Clear current input
                while (input.length > 0) {
                  newTerm.write('\b \b');
                  setInput(prev => prev.slice(0, -1));
                }
                
                // Write history command
                newTerm.write(historyCommand);
                setInput(historyCommand);
              }
            } else if (ev.keyCode === 40) { // Down arrow
              // Navigate history
              if (historyIndex > 0) {
                const newIndex = historyIndex - 1;
                setHistoryIndex(newIndex);
                const historyCommand = history[history.length - 1 - newIndex];
                
                // Clear current input
                while (input.length > 0) {
                  newTerm.write('\b \b');
                  setInput(prev => prev.slice(0, -1));
                }
                
                // Write history command
                newTerm.write(historyCommand);
                setInput(historyCommand);
              } else if (historyIndex === 0) {
                // Clear input when reaching the end of history
                while (input.length > 0) {
                  newTerm.write('\b \b');
                  setInput(prev => prev.slice(0, -1));
                }
                setHistoryIndex(-1);
              }
            } else if (printable) {
              setInput(prev => prev + e.key);
              newTerm.write(e.key);
            }
          });

          // Focus the terminal
          setTimeout(() => {
            if (newTerm) {
              newTerm.focus();
              console.log("Terminal focused");
            }
          }, 100);
        }
      } catch (error) {
        console.error('Error initializing terminal:', error);
      }
    };

    initTerminal();

    // Clean up on unmount or when terminal is hidden
    return () => {
      console.log("Cleaning up terminal");
      isComponentMounted = false;

      if (newTerm) {
        newTerm.dispose();
      }

      setTerm(null);
      setFitAddon(null);
    };
  }, [isVisible, input, history, historyIndex]);

  // Process commands
  const processCommand = (command: string, terminal: any) => {
    const cmd = command.trim().toLowerCase();
    
    if (!cmd) {
      return;
    }
    
    if (cmd === 'help') {
      terminal.writeln('Available commands:');
      terminal.writeln('  help     - Show this help message');
      terminal.writeln('  clear    - Clear the terminal');
      terminal.writeln('  echo     - Echo text back to the terminal');
      terminal.writeln('  date     - Show current date and time');
      terminal.writeln('  whoami   - Show current user');
    } else if (cmd === 'clear') {
      terminal.clear();
    } else if (cmd.startsWith('echo ')) {
      const text = command.substring(5);
      terminal.writeln(text);
    } else if (cmd === 'date') {
      terminal.writeln(new Date().toString());
    } else if (cmd === 'whoami') {
      terminal.writeln('user');
    } else {
      terminal.writeln(`Command not found: ${command}`);
    }
  };

  // Handle keyboard shortcut (F12) to toggle terminal
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'F12') {
        e.preventDefault();
        if (isVisible) {
          onClose();
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [isVisible, onClose]);

  // Handle resizing
  const handleResizeStart = (e: React.MouseEvent) => {
    setIsResizing(true);
    resizeStartYRef.current = e.clientY;
    initialHeightRef.current = height;
  };

  useEffect(() => {
    const handleResizeMove = (e: MouseEvent) => {
      if (!isResizing) return;

      const deltaY = resizeStartYRef.current - e.clientY;
      const newHeight = Math.max(150, initialHeightRef.current + deltaY);
      setHeight(newHeight);
    };

    const handleResizeEnd = () => {
      setIsResizing(false);
    };

    if (isResizing) {
      document.addEventListener('mousemove', handleResizeMove);
      document.addEventListener('mouseup', handleResizeEnd);
    }

    return () => {
      document.removeEventListener('mousemove', handleResizeMove);
      document.removeEventListener('mouseup', handleResizeEnd);
    };
  }, [isResizing]);

  // Update the CSS variable for height using a ref
  useEffect(() => {
    if (terminalRef.current && terminalRef.current.parentElement) {
      const container = terminalRef.current.parentElement;
      container.style.setProperty('--terminal-height', `${height}px`);
    }
  }, [height]);

  if (!isVisible) return null;

  return (
    <div className={styles.terminalContainer}>
      <div className={styles.terminalHeader}>
        <div className={styles.terminalTitle}>Simple Terminal</div>
        <button
          type="button"
          className={styles.closeButton}
          onClick={onClose}
          aria-label="Close terminal"
        >
          ×
        </button>
      </div>
      <div
        className={styles.resizeHandle}
        onMouseDown={handleResizeStart}
      ></div>
      <div
        ref={terminalRef}
        className={styles.terminal}
        tabIndex={-1} // Make it focusable
      ></div>
    </div>
  );
};

export default SimpleTerminal;
