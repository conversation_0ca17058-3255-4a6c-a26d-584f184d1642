import { invoke as tauriInvoke } from '@tauri-apps/api/core';
import { listen as tauriListen } from '@tauri-apps/api/event';

// Define a type for the electronAPI exposed on the window
interface ElectronAPI {
  loadApiKeys: () => Promise<ApiKeys>;
  saveApiKeys: (keys: ApiKeys) => Promise<{ success: boolean; error?: string }>;
  getOllamaModels: () => Promise<string[] | { error: string }>;
  sendChatMessage: (params: SendChatParams) => Promise<string | { error: string }>;
  // Add other methods here as they are migrated
}

declare global {
  interface Window {
    electronAPI?: ElectronAPI;
  }
}

// Re-export invoke for convenience, typed appropriately
// This will primarily be used for Tauri-specific commands or as a fallback.
export const invoke = <T>(cmd: string, args?: Record<string, unknown>): Promise<T> => {
  // If electronAPI is available and a mapping exists for this cmd, use it.
  // For now, we'll handle specific functions directly.
  // This specific invoke function will likely be phased out as individual commands are migrated.
  return tauriInvoke(cmd, args);
};

// Re-export listen for convenience
export const listen = tauriListen;

/**
 * Terminal API functions
 */

/**
 * Creates a new terminal shell
 * @returns {Promise<void>}
 */
export async function createTerminalShell(): Promise<void> {
  try {
    await invoke<void>('create_terminal_shell');
  } catch (error) {
    console.error("Error creating terminal shell:", error);
    throw error;
  }
}

/**
 * Writes data to the terminal
 * @param {string} data - The data to write to the terminal
 * @returns {Promise<void>}
 */
export async function writeToTerminal(data: string): Promise<void> {
  try {
    await invoke<void>('write_to_terminal', { data });
  } catch (error) {
    console.error("Error writing to terminal:", error);
    throw error;
  }
}

/**
 * Reads data from the terminal
 * @returns {Promise<string|null>}
 */
export async function readFromTerminal(): Promise<string | null> {
  try {
    return await invoke<string | null>('read_from_terminal');
  } catch (error) {
    console.error("Error reading from terminal:", error);
    throw error;
  }
}

/**
 * Resizes the terminal
 * @param {number} rows - The number of rows
 * @param {number} cols - The number of columns
 * @returns {Promise<void>}
 */
export async function resizeTerminal(rows: number, cols: number): Promise<void> {
  try {
    await invoke<void>('resize_terminal', { rows, cols });
  } catch (error) {
    console.error("Error resizing terminal:", error);
    throw error;
  }
}

// Matches ImageDialogResponseRust in page.tsx and Rust struct
export interface ImageDialogResponse {
  file_path: string | null;
  file_name: string | null;
  mime_type: string | null;
  base64_content: string | null;
  data_url: string | null;
}

// Matches ApiKeys in page.tsx and Rust struct
export interface ApiKeys {
  openrouter_key?: string;
  gemini_key?: string;
  ollama_url?: string;
  lm_studio_url?: string;
}

// Define OllamaMessage structure, mirroring Rust and frontend
export interface OllamaMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
  images?: string[]; // Base64 encoded images, optional
}

export interface SendChatParams {
  provider: string;
  modelName: string;
  promptText?: string; // Make optional, as Ollama will use messages
  messages?: OllamaMessage[]; // For Ollama /api/chat
  imageDataUrl?: string | null; // Still used for non-Ollama image uploads or simple Ollama user messages
}

/**
 * Invokes the Rust command 'select_image_file' to open a dialog and get image details.
 */
export async function selectImageFile(): Promise<ImageDialogResponse | null> {
  try {
    const result = await invoke<ImageDialogResponse>('select_image_file');
    return result;
  } catch (error) {
    console.error("Error invoking 'select_image_file' Rust command:", error);
    // Consider how to propagate error type if needed, for now null as per original JS
    return null;
  }
}

/**
 * Wraps the 'send_chat_message' Rust command to send a prompt and optional image to an LLM.
 */
export async function sendChatWithImage(params: SendChatParams): Promise<string> {
  const { provider, modelName, promptText, messages, imageDataUrl } = params;

  if (!provider || !modelName) {
    const errorMsg = "Provider and model name are required for sendChatWithImage.";
    console.error(errorMsg);
    return Promise.reject(new Error(errorMsg));
  }

  // Validate that either promptText or messages is provided
  if (provider !== 'Ollama' && !promptText) {
    const errorMsg = "promptText is required for non-Ollama providers.";
    console.error(errorMsg);
    return Promise.reject(new Error(errorMsg));
  }
  if (provider === 'Ollama') {
    // For Ollama, if messages are empty, we might still use promptText for a simple single message.
    // The Rust backend currently expects promptText and constructs a simple message array.
    // This part needs careful coordination with how Rust `send_chat_message` for Ollama is finalized.
    // For now, let's assume if it's Ollama, `messages` should ideally be used,
    // but Rust side currently takes `promptText` and converts it.
    // To make this cleaner, Rust should ideally take `messages_json: Option<String>` for Ollama.
    // Let's stick to sending `promptText` for now, and Rust will wrap it.
    // This means frontend needs to ensure `promptText` is correctly formatted if it's for Ollama /api/chat
    // OR, we adjust Rust to take a serialized Vec<OllamaMessage> as promptText for Ollama.
    // Given the current Rust code, promptText will be the content of the last user message.
    if (!promptText && (!messages || messages.length === 0)) {
      const errorMsg = "For Ollama, either promptText (for simple) or messages (for chat) must be provided.";
      console.error(errorMsg);
      return Promise.reject(new Error(errorMsg));
    }
  }


  // The Rust backend's send_chat_message for Ollama currently takes `prompt_text: String`
  // and constructs a single user message. For a true /api/chat implementation,
  // the frontend should prepare the full `OllamaMessage[]` and serialize it as JSON
  // into `promptText` if provider is Ollama, OR the Rust command signature should change.
  // Let's assume for now that if provider is Ollama, `promptText` will be the content of the latest user message,
  // and `imageDataUrl` will be handled by Rust to be part of that message.
  // The `messages` param in SendChatParams is for future direct use if Rust is updated.

  // Construct the payload for both Electron and Tauri
  const payload: SendChatParams = { // Use SendChatParams directly as it matches the expected structure
    provider,
    modelName,
    imageDataUrl, // This will be used by the main process if applicable
  };

  if (provider === 'Ollama') {
    if (messages && messages.length > 0) {
      try {
        // For Electron, promptText will be the stringified messages.
        // For Tauri, the Rust backend also expects promptText to be this stringified array.
        payload.promptText = JSON.stringify(messages);
      } catch (e) {
        console.error("Failed to stringify Ollama messages:", e);
        return Promise.reject(new Error("Failed to stringify Ollama messages for /api/chat."));
      }
    } else if (promptText) {
      payload.promptText = promptText;
    } else {
      return Promise.reject(new Error("For Ollama, promptText or messages must be provided."));
    }
  } else {
    payload.promptText = promptText;
  }

  if (window.electronAPI && typeof window.electronAPI.sendChatMessage === 'function') {
    try {
      console.log("Using Electron IPC for sendChatMessage", payload);
      const result = await window.electronAPI.sendChatMessage(payload);
      if (typeof result === 'string') {
        return result;
      } else if (result && typeof result === 'object' && 'error' in result && result.error) {
        throw new Error(result.error as string);
      }
      throw new Error("Invalid response from Electron sendChatMessage");
    } catch (error) {
      console.error("Error using Electron IPC for sendChatMessage:", error);
      if (error instanceof Error) throw error;
      throw new Error(String(error));
    }
  } else {
    // Fallback to Tauri
    console.log("Using Tauri IPC for sendChatMessage", payload);
    try {
      // Tauri's invoke expects the arguments as the second parameter.
      // The Rust command `send_chat_message` takes individual arguments.
      // The `invoke` wrapper in `tauri-api.ts` currently passes `args` directly.
      // We need to ensure the payload matches what the Rust command expects if not using Electron.
      // The Rust command is: send_chat_message(app_handle, provider, model_name, prompt_text, image_data_url)
      // So the tauriInvoke should be: tauriInvoke('send_chat_message', payload)
      const responseText = await tauriInvoke<string>('send_chat_message', payload as unknown as Record<string, unknown>);
      return responseText;
    } catch (error) {
      console.error("Error invoking 'send_chat_message' Tauri command:", error);
      if (typeof error === 'string') {
        throw new Error(error);
      }
      throw error;
    }
  }
}

/**
 * Invokes the Rust command 'load_api_keys' to retrieve stored API keys.
 */
export async function loadApiKeys(): Promise<ApiKeys> {
  if (window.electronAPI && typeof window.electronAPI.loadApiKeys === 'function') {
    try {
      console.log("Using Electron IPC for loadApiKeys");
      const result = await window.electronAPI.loadApiKeys();
      // The main process might return { error: message } on failure
      if (result && typeof result === 'object' && 'error' in result && result.error) {
        throw new Error(result.error as string);
      }
      return result || {};
    } catch (error) {
      console.error("Error using Electron IPC for loadApiKeys:", error);
      if (error instanceof Error) throw error;
      throw new Error(String(error));
    }
  } else {
    // Fallback to Tauri
    console.log("Using Tauri IPC for loadApiKeys");
    try {
      const keys = await tauriInvoke<ApiKeys>('load_api_keys');
      return keys || {}; // Ensure an object is returned
    } catch (error) {
      console.error("Error invoking 'load_api_keys' Tauri command:", error);
      if (typeof error === 'string') {
        throw new Error(error);
      }
      throw error;
    }
  }
}

/**
 * Invokes the Rust command 'get_google_gemini_models' to fetch available models from Google Gemini.
 * @returns {Promise<string[]>} A promise that resolves to an array of model name strings.
 */
export async function getGoogleGeminiModels(): Promise<string[]> {
  try {
    const models = await invoke<string[]>('get_google_gemini_models');
    return models || []; // Ensure an array is returned
  } catch (error) {
    console.error("Error invoking 'get_google_gemini_models' Rust command:", error);
    if (typeof error === 'string') {
      throw new Error(error);
    }
    throw error;
  }
}

/**
 * Invokes the Rust command 'save_api_keys' to store API keys.
 */
export async function saveApiKeys(keys: ApiKeys): Promise<void> {
  if (typeof keys !== 'object' || keys === null) {
    const errorMsg = "Invalid argument: 'keys' must be an object.";
    console.error(errorMsg);
    return Promise.reject(new Error(errorMsg));
  }

  if (window.electronAPI && typeof window.electronAPI.saveApiKeys === 'function') {
    try {
      console.log("Using Electron IPC for saveApiKeys");
      const result = await window.electronAPI.saveApiKeys(keys);
      if (!result.success) {
        throw new Error(result.error || 'Failed to save API keys via Electron IPC');
      }
      return;
    } catch (error) {
      console.error("Error using Electron IPC for saveApiKeys:", error);
      if (error instanceof Error) throw error;
      throw new Error(String(error));
    }
  } else {
    // Fallback to Tauri
    console.log("Using Tauri IPC for saveApiKeys");
    try {
      // The Tauri command `save_api_keys` expects an argument `keys: ApiKeys` directly in the args object.
      // The Rust backend for save_api_keys takes `app_handle: AppHandle, keys: ApiKeys`
      // So the frontend call should be invoke('save_api_keys', { keys: actualKeysObject })
      // However, the original Rust command `save_api_keys` in lib.rs takes `keys: ApiKeys` as the second argument,
      // not nested. The `invoke` wrapper in Tauri handles this.
      // The payload for Tauri's invoke should be the arguments object.
      // If the Rust command is `fn save_api_keys(app_handle: AppHandle, keys: ApiKeys)`,
      // then `invoke('save_api_keys', { keys })` is correct.
      // If the Rust command is `fn save_api_keys(app_handle: AppHandle, openrouter_key: Option<String>, gemini_key: Option<String> ...)`
      // then `invoke('save_api_keys', keys)` where keys is the ApiKeys object itself is correct.
      // Looking at the Rust code: `async fn save_api_keys(app_handle: AppHandle, keys: ApiKeys)`
    // So, the arguments object passed to invoke should contain a field named `keys` whose value is the ApiKeys object.
    await tauriInvoke<void>('save_api_keys', { keys }); // Pass as { keys: keys }
  } catch (error) {
    console.error("Error invoking 'save_api_keys' Tauri command:", error);
      if (typeof error === 'string') {
        throw new Error(error);
      }
      throw error;
    }
  }
}

/**
 * Invokes the Rust command 'overwrite_file_content' to save content to an existing file.
 */
export async function overwriteExistingFile(path: string, content: string): Promise<void> {
  if (typeof path !== 'string' || !path) {
    // It's better to throw an error or return a Promise.reject for async functions
    return Promise.reject(new Error("Invalid argument: 'path' must be a non-empty string."));
  }
  if (typeof content !== 'string') {
    return Promise.reject(new Error("Invalid argument: 'content' must be a string."));
  }
  try {
    await invoke<void>('overwrite_file_content', { path, content });
  } catch (error) {
    console.error("Error invoking 'overwrite_file_content' Rust command:", error);
    if (typeof error === 'string') {
      throw new Error(error);
    }
    throw error;
  }
}

// This function is not yet migrated to Electron IPC, so it will use Tauri's invoke.
// We'll add the conditional logic once the Electron main process has get_ollama_models.
export async function getOllamaModels(): Promise<string[]> {
  if (window.electronAPI && typeof window.electronAPI.getOllamaModels === 'function') {
    try {
      console.log("Using Electron IPC for getOllamaModels");
      const result = await window.electronAPI.getOllamaModels();
      if (Array.isArray(result)) {
        return result;
      } else if (result && typeof result === 'object' && 'error' in result && result.error) {
        throw new Error(result.error as string);
      }
      throw new Error("Invalid response from Electron getOllamaModels");
    } catch (error) {
      console.error("Error using Electron IPC for getOllamaModels:", error);
      if (error instanceof Error) throw error;
      throw new Error(String(error));
    }
  } else {
    // Fallback to Tauri
    console.log("Using Tauri IPC for getOllamaModels");
    try {
      const models = await tauriInvoke<string[]>('get_ollama_models');
      return models || [];
    } catch (error) {
      console.error("Error invoking 'get_ollama_models' Tauri command:", error);
      if (typeof error === 'string') {
        throw new Error(error);
      }
      throw error;
    }
  }
}

export interface CodeFile {
  path: string;
  content: string;
}

/**
 * Invokes the Rust command 'select_code_entity' to allow the user to select multiple code files.
 * @returns {Promise<CodeFile[]>} A promise that resolves to an array of CodeFile objects.
 */
export async function selectCodeEntity(): Promise<CodeFile[]> {
  try {
    const files = await invoke<CodeFile[]>('select_code_entity');
    return files || []; // Ensure an array is returned, even if null/undefined from invoke
  } catch (error) {
    console.error("Error invoking 'select_code_entity' Rust command:", error);
    if (typeof error === 'string') {
      throw new Error(error);
    }
    throw error;
  }
}

/**
 * Invokes the Rust command 'get_openrouter_models' to fetch available models from OpenRouter.
 */
export async function getOpenRouterModels(): Promise<string[]> {
  try {
    const models = await invoke<string[]>('get_openrouter_models');
    return models || []; // Ensure an array is returned
  } catch (error) {
    console.error("Error invoking 'get_openrouter_models' Rust command:", error);
    if (typeof error === 'string') {
      throw new Error(error);
    }
    throw error;
  }
}
