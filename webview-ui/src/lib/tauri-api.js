import { invoke as tauriInvoke } from '@tauri-apps/api/core'; // Corrected for Tauri v2

// Re-export invoke, as page.tsx might be using it.
export const invoke = tauriInvoke;

/**
 * Terminal API functions
 */

/**
 * Creates a new terminal shell
 * @returns {Promise<void>}
 */
export async function createTerminalShell() {
  try {
    await invoke('create_terminal_shell');
  } catch (error) {
    console.error("Error creating terminal shell:", error);
    throw error;
  }
}

/**
 * Writes data to the terminal
 * @param {string} data - The data to write to the terminal
 * @returns {Promise<void>}
 */
export async function writeToTerminal(data) {
  try {
    await invoke('write_to_terminal', { data });
  } catch (error) {
    console.error("Error writing to terminal:", error);
    throw error;
  }
}

/**
 * Reads data from the terminal
 * @returns {Promise<string|null>}
 */
export async function readFromTerminal() {
  try {
    return await invoke('read_from_terminal');
  } catch (error) {
    console.error("Error reading from terminal:", error);
    throw error;
  }
}

/**
 * Resizes the terminal
 * @param {number} rows - The number of rows
 * @param {number} cols - The number of columns
 * @returns {Promise<void>}
 */
export async function resizeTerminal(rows, cols) {
  try {
    await invoke('resize_terminal', { rows, cols });
  } catch (error) {
    console.error("Error resizing terminal:", error);
    throw error;
  }
}

/**
 * Invokes the Rust command 'select_image_file' to open a dialog and get image details.
 * The Rust command handles file reading and provides base64 data.
 * @returns {Promise<{file_path: string, file_name: string, mime_type: string, base64_content: string, data_url: string} | null>}
 * This structure should align with the 'ImageDialogResponseRust' type in your TSX file.
 * Note: Rust Option<T> becomes T | null in JS.
 */
export async function selectImageFile() {
  try {
    // Calls the existing 'select_image_file' Rust command.
    const result = await invoke('select_image_file');
    // If the user cancels the dialog, the Rust command returns an object with None/null fields.
    // We return the result directly; the caller should check if a file was selected (e.g., by checking result.file_path).
    return result;
  } catch (error) {
    console.error("Error invoking 'select_image_file' Rust command:", error);
    return null; // Or rethrow, or return an error structure
  }
}

/**
 * Wraps the 'send_chat_message' Rust command to send a prompt and optional image/messages to an LLM.
 * @param {object} params - Parameters for sending the chat message.
 * @param {string} params.provider - E.g., "Ollama", "LM Studio".
 * @param {string} params.modelName - The specific model to use.
 * @param {string} [params.promptText] - The user's text prompt (used by most providers, or as fallback for Ollama).
 * @param {Array<{role: 'system'|'user'|'assistant', content: string, images?: string[]}>} [params.messages] - Array of message objects, primarily for Ollama /api/chat.
 * @param {string} [params.imageDataUrl] - Optional. The data_url of the image.
 * @returns {Promise<string>} - The LLM's response text.
 */
export async function sendChatWithImage(params) {
  const { provider, modelName, promptText, messages, imageDataUrl } = params;

  if (!provider || !modelName) {
    const errorMsg = "Provider and model name are required for sendChatWithImage.";
    console.error(errorMsg);
    return Promise.reject(new Error(errorMsg));
  }

  if (provider !== 'Ollama' && !promptText) {
    const errorMsg = "promptText is required for non-Ollama providers.";
    console.error(errorMsg);
    return Promise.reject(new Error(errorMsg));
  }
  if (provider === 'Ollama' && !promptText && (!messages || messages.length === 0)) {
    const errorMsg = "For Ollama, either promptText or a messages array must be provided.";
    console.error(errorMsg);
    return Promise.reject(new Error(errorMsg));
  }

  const payload = {
    provider,
    modelName,
    imageDataUrl, // imageDataUrl is passed for all providers; Rust side will use it if applicable
  };

  if (provider === 'Ollama') {
    if (messages && messages.length > 0) {
      // If a messages array is provided, serialize it and send as promptText.
      // Rust will attempt to deserialize this.
      try {
        payload.promptText = JSON.stringify(messages);
      } catch (e) {
        console.error("Failed to stringify Ollama messages:", e);
        return Promise.reject(new Error("Failed to stringify Ollama messages for /api/chat."));
      }
    } else if (promptText) {
      // Fallback: if no messages array, but promptText exists, send it.
      // Rust will wrap this into a single user message.
      payload.promptText = promptText;
    } else {
      // This case should have been caught by earlier validation
      return Promise.reject(new Error("For Ollama, promptText or messages must be provided."));
    }
  } else {
    // For other providers, promptText is mandatory.
    payload.promptText = promptText;
  }

  try {
    const responseText = await invoke('send_chat_message', payload);
    return responseText;
  } catch (error) {
    console.error("Error invoking 'send_chat_message' Rust command:", error);
    // The error from Rust is already a String (due to -> Result<String, String>)
    throw error; // Rethrow the error string to be caught by the caller
  }
}

/**
 * @typedef {object} ApiKeys
 * @property {string} [openrouter_key]
 * @property {string} [gemini_key]
 */

/**
 * Invokes the Rust command 'load_api_keys' to retrieve stored API keys.
 * @returns {Promise<ApiKeys>} A promise that resolves to an object containing the API keys.
 *                              Returns an empty object or default keys if none are stored.
 */
export async function loadApiKeys() {
  try {
    const keys = await invoke('load_api_keys');
    return keys || {}; // Ensure an object is returned even if Rust returns null
  } catch (error) {
    console.error("Error invoking 'load_api_keys' Rust command:", error);
    throw error; // Rethrow to be handled by the caller
  }
}

/**
 * Invokes the Rust command 'save_api_keys' to store API keys.
 * @param {ApiKeys} keys - An object containing the API keys to save.
 * @returns {Promise<void>} A promise that resolves when keys are saved.
 */
export async function saveApiKeys(keys) {
  if (typeof keys !== 'object' || keys === null) {
    const errorMsg = "Invalid argument: 'keys' must be an object.";
    console.error(errorMsg);
    return Promise.reject(new Error(errorMsg));
  }
  try {
    await invoke('save_api_keys', { keys }); // Pass as { keys: keys } to match Rust command signature
  } catch (error) {
    console.error("Error invoking 'save_api_keys' Rust command:", error);
    throw error; // Rethrow to be handled by the caller
  }
}

/**
 * Invokes the Rust command 'overwrite_file_content' to save content to an existing file.
 * @param {string} path - The absolute path to the file.
 * @param {string} content - The content to write to the file.
 * @returns {Promise<void>} A promise that resolves when the file is written.
 */
export async function overwriteExistingFile(path, content) {
  if (typeof path !== 'string' || !path) {
    return Promise.reject(new Error("Invalid argument: 'path' must be a non-empty string."));
  }
  if (typeof content !== 'string') {
    return Promise.reject(new Error("Invalid argument: 'content' must be a string."));
  }
  try {
    await invoke('overwrite_file_content', { path, content });
  } catch (error) {
    console.error("Error invoking 'overwrite_file_content' Rust command:", error);
    throw error; // Rethrow to be handled by the caller
  }
}

/**
 * Invokes the Rust command 'select_code_entity' to allow the user to select multiple code files.
 * @returns {Promise<Array<{path: string, content: string}>>} A promise that resolves to an array of objects,
 * each containing the file path and its content. Returns an empty array if cancelled or on error for simplicity.
 */
export async function selectCodeEntity() {
  try {
    const files = await invoke('select_code_entity');
    return files || []; // Ensure an array is returned
  } catch (error) {
    console.error("Error invoking 'select_code_entity' Rust command:", error);
    // Depending on desired error handling, you might throw, or return empty/error indicator
    return []; // Return empty array on error for now
  }
}

/**
 * Invokes the Rust command 'get_openrouter_models' to fetch available models from OpenRouter.
 * @returns {Promise<string[]>} A promise that resolves to an array of model ID strings.
 */
export async function getOpenRouterModels() {
  try {
    const models = await invoke('get_openrouter_models');
    return models || []; // Ensure an array is returned
  } catch (error) {
    console.error("Error invoking 'get_openrouter_models' Rust command:", error);
    throw error; // Rethrow to be handled by the caller
  }
}

/**
 * Invokes the Rust command 'get_google_gemini_models' to fetch available models from Google Gemini.
 * @returns {Promise<string[]>} A promise that resolves to an array of model ID strings.
 */
export async function getGoogleGeminiModels() {
  try {
    const models = await invoke('get_google_gemini_models');
    return models || []; // Ensure an array is returned
  } catch (error) {
    console.error("Error invoking 'get_google_gemini_models' Rust command:", error);
    throw error; // Rethrow to be handled by the caller
  }
}
