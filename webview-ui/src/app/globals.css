html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  padding: 0;
  margin: 0;
  height: 100%; /* Ensure body takes full height for the flex container */
  background-color: #1a1f2e; /* Dark blue background */
}

* {
  box-sizing: border-box;
}

/* Allow VS Code theme variables to take precedence */
body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  color: #ffffff;
  background-color: #1a1f2e; /* Dark blue background */
}

a {
  color: #6c7ee1;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

a:active {
  color: #4a5cd1;
}

.lugari-app-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #1a1f2e;
}
