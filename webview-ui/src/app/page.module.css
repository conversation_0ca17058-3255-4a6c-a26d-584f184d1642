.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  padding: 10px;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  background-color: #1a1f2e;
  color: #ffffff;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 10px;
  border-bottom: 1px solid #2a3042;
  margin-bottom: 10px;
  gap: 20px;
}

.headerLeftControls {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.controlsRow {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
  align-items: center;
}

.select {
  padding: 8px 12px;
  border: 1px solid #2a3042;
  background-color: #2a3042;
  color: #ffffff;
  border-radius: 4px;
  cursor: pointer;
}

.button {
  padding: 8px 12px;
  border: 1px solid #2a3042;
  background-color: #2a3042;
  color: #ffffff;
  border-radius: 4px;
  cursor: pointer;
}

.input {
  padding: 8px 12px;
  border: 1px solid #2a3042;
  background-color: #2a3042;
  color: #ffffff;
  border-radius: 4px;
}

.iconButton {
  padding: 8px 12px;
  border: 1px solid #2a3042;
  background-color: #2a3042;
  color: #ffffff;
  border-radius: 4px;
  cursor: pointer;
}

.button:hover,
.iconButton:hover,
.select:hover {
  background-color: #3a4052;
}

/* Mode buttons */
.modeButtons {
  display: flex;
  gap: 10px;
}

.draftButton {
  background-color: #2a3042;
  color: #ffffff;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}

.implementButton {
  background-color: #2a3042;
  color: #ffffff;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}

.draftButton.active {
  background-color: #f0a500;
  color: #ffffff;
}

.implementButton.active {
  background-color: #00c853;
  color: #ffffff;
}

.chatArea {
  flex-grow: 1;
  overflow-y: auto;
  margin-bottom: 10px;
  padding: 10px;
  border: 1px solid #2a3042;
  border-radius: 4px;
  background-color: #1a1f2e;
}

.message {
  padding: 12px;
  margin-bottom: 12px;
  border-radius: 6px;
}

.message.user {
  background-color: rgba(0, 0, 0, 0.3);
  color: #FFFFFF;
  border: 1px solid #f0a500;
  text-align: right;
  margin-left: 20%;
}

.message.lugari {
  background-color: rgba(0, 0, 0, 0.3);
  color: #FFFFFF;
  border: 1px solid #00c853;
  margin-right: 20%;
}

.footer {
  padding-top: 10px;
  border-top: 1px solid #2a3042;
}

.inputForm {
  display: flex;
  gap: 10px;
}

.input {
  flex-grow: 1;
  padding: 12px;
  border-radius: 6px;
  background-color: #2a3042;
  color: #ffffff;
  border: 1px solid #2a3042;
}

.errorText {
  color: #ff4c4c;
  padding: 8px;
  font-size: 0.9em;
  margin-bottom: 5px;
  min-height: 1.2em;
  background-color: rgba(255, 76, 76, 0.1);
  border-radius: 4px;
}

/* Styles for Draft Mode */
.draftHighlightBorder {
  border: 2px solid #f0a500 !important; /* Orange border */
  box-shadow: 0 0 5px #f0a500; /* Optional: adds a glow */
}

.draftActiveButton {
  background-color: #f0a500; /* Orange fill */
  border: none;
  color: #ffffff; /* White text for contrast */
}

/* Styles for Implement Mode */
.implementHighlightBorder {
  border: 2px solid #00c853 !important; /* Green border */
  box-shadow: 0 0 5px #00c853; /* Optional: adds a glow */
}

.implementActiveButton {
  background-color: #00c853; /* Green fill */
  border: none;
  color: #ffffff; /* White text for contrast */
}

/* Mode buttons */
.modeButtons {
  display: flex;
  gap: 10px;
}

.draftButton {
  background-color: #2a3042;
  color: #ffffff;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  min-width: 100px;
  text-align: center;
}

.implementButton {
  background-color: #2a3042;
  color: #ffffff;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  min-width: 100px;
  text-align: center;
}

.draftButton.active {
  background-color: #f0a500;
  color: #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.implementButton.active {
  background-color: #2a3042;
  color: #ffffff;
  border: 1px solid #ffffff;
}

/* Styling for code blocks within Lugari messages */
.message.lugari :global(pre.hljs) { /* Use :global for external hljs classes */
  padding: 1em;
  margin-top: 0.8em;
  margin-bottom: 0.8em;
  border-radius: 6px; /* Match message bubble or slightly different */
  /* background-color: #282c34; /* Atom One Dark default, theme CSS should handle this */
  border: 1px solid var(--vscode-editorWidget-border, #5c6370); /* Separator border */
  overflow-x: auto; /* Allow horizontal scrolling for long lines */
  white-space: pre-wrap; /* Allow wrapping of long lines */
  word-break: break-all; /* Break long words if necessary to prevent overflow */
}

/* Ensure code tag inside pre also uses a monospace font consistent with editor */
.message.lugari :global(pre.hljs code.hljs) { /* Target code tag if hljs also adds class there */
  font-family: var(--vscode-editor-font-family, 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace);
  font-size: 0.9em; /* Or adjust to preference */
  /* The color of text tokens is handled by highlight.js theme */
}

/* Styling for inline code (e.g., terminal commands) within Lugari messages */
/* This targets <code> tags not inside a <pre class="hljs"> block */
.message.lugari > span > p > code, /* Common for MD paragraphs */
.message.lugari > span > ul > li > code, /* Common for MD list items */
.message.lugari > span > ol > li > code,
.message.lugari > span > blockquote > p > code, /* Common for MD blockquotes */
.message.lugari > span > code /* For cases where code might be a direct child of the span */ {
  background-color: rgba(70, 70, 70, 0.75); /* Adjusted for more presence */
  padding: 0.2em 0.4em;
  margin: 0 0.1em;
  font-size: 0.9em; /* Consistent with fenced code blocks or slightly smaller */
  border-radius: 4px;
  font-family: var(--vscode-editor-font-family, 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace);
  color: var(--vscode-textPreformat-foreground, #d4d4d4); /* Use a VSCode-like preformat color */
  border: 1px solid rgba(130, 130, 130, 0.6); /* Slightly more defined border */
}

.imageNameLabel {
  font-size: 0.8em;
  color: var(--vscode-descriptionForeground, #888888); /* Dim color for info text */
  margin-left: 10px; /* Align with select boxes or adjust as needed */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px; /* Prevent very long names from breaking layout */
}

.noMarginBottom {
  margin-bottom: 0 !important; /* Use !important if needed to override existing .controlsRow margin, or ensure specificity */
}

.statusMessage {
  color: #ffffff;
  padding: 8px;
  font-size: 0.9em;
  text-align: left;
  margin-bottom: 5px;
  min-height: 1.2em;
  background-color: rgba(108, 126, 225, 0.1);
  border-radius: 4px;
}

.controlsLabel {
  color: var(--vscode-input-foreground, #cccccc); /* Match other control text */
  font-size: 0.9em;
  display: flex;
  align-items: center;
  gap: 5px;
}

.ingestedFilesSection {
  margin-top: 5px;
  margin-bottom: 10px;
  padding: 8px;
  border: 1px solid var(--vscode-editorWidget-border, #454545);
  border-radius: 4px;
  max-height: 150px; /* Or a height that fits your layout */
  display: flex;
  flex-direction: column;
}

.ingestedFilesHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.ingestedFilesLabel {
  font-weight: bold;
  font-size: 0.9em;
  color: var(--vscode-input-foreground, #cccccc);
}

.ingestedFilesControls {
  display: flex;
  gap: 5px;
}

.smallButton {
  padding: 2px 6px;
  font-size: 0.8em;
  border: 1px solid var(--vscode-input-border, #3c3c3c);
  background-color: var(--vscode-input-background, #3c3c3c);
  color: var(--vscode-input-foreground, #cccccc);
  border-radius: 3px;
}

.smallButton:hover {
  background-color: var(--vscode-button-hoverBackground, #4c4c4c);
}

.ingestedFilesListWithCheckboxes {
  list-style: none;
  padding: 0;
  margin: 0;
  overflow-y: auto; /* Add scroll for long lists */
  font-size: 0.9em;
}

.ingestedFilesListWithCheckboxes li {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 3px 0;
  color: var(--vscode-input-foreground, #cccccc);
}

.ingestedFilesListWithCheckboxes input[type="checkbox"] {
  margin-right: 5px;
  accent-color: var(--vscode-button-background, #0e639c); /* Style checkbox color */
}

.ingestedFilesListWithCheckboxes label {
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Chat sidebar styles */
.chatSidebarToggle {
  background: none;
  border: none;
  color: #ffffff;
  cursor: pointer;
  font-size: 20px;
  padding: 5px;
  margin-left: 10px;
}

.chatSidebarIcon {
  width: 24px;
  height: 24px;
}

/* Code editor area */
.codeEditorArea {
  margin-top: 20px;
  border: 1px solid #2a3042;
  border-radius: 6px;
  padding: 15px;
  background-color: #1a1f2e;
}

.codeEditorArea h4 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #ffffff;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.closeFileButton {
  background: none;
  border: none;
  color: #a0a0a0;
  font-size: 18px;
  cursor: pointer;
  padding: 0 5px;
  line-height: 1;
}

.closeFileButton:hover {
  color: #ffffff;
}

.codeEditorButtonsContainer {
  margin-top: 10px;
  display: flex;
  gap: 10px;
}

/* Debug panel styles */
.debugPanel {
  position: fixed;
  bottom: 10px;
  left: 10px;
  right: 10px;
  max-height: 40vh;
  overflow-y: auto;
  background-color: rgba(0,0,0,0.85);
  color: lightgreen;
  border: 2px solid red;
  padding: 10px;
  z-index: 9999;
  font-size: 12px;
  font-family: monospace;
}

.debugPre {
  white-space: pre-wrap;
  word-break: break-all;
}

.debugPreError {
  color: orange;
}

.debugPreSuccess {
  color: lightgreen;
}

.debugTextarea {
  width: 100%;
  height: 100px;
  background-color: #222;
  color: lightgreen;
  border: 1px solid #444;
  white-space: pre;
  word-break: break-all;
}

/* Floating action buttons */
.floatingButtonsContainer {
  position: fixed;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 15px;
  z-index: 100;
}

.floatingButton {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: rgba(42, 48, 66, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 20px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.floatingButton:hover {
  background-color: rgba(42, 48, 66, 1);
  transform: scale(1.05);
}

.floatingButton.active {
  background-color: #6c7ee1;
}

.floatingButtonIcon {
  font-size: 22px;
}
