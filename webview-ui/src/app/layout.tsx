'use client'; // Required for useState and useEffect
import type { Metadata } from "next"; // Keep for now, though might not be used directly
import React, { useState, useEffect, ComponentType } from "react"; // Import useState, useEffect
import dynamic, { type DynamicOptions, type Loader } from 'next/dynamic'; // Restoring dynamic import with explicit types
import "./globals.css";
// import YakuakeTerminal from "../components/YakuakeTerminal.tsx"; // Will be dynamic

// Define props interface for YakuakeTerminal if not already imported from the component itself
// Or import it if YakuakeTerminal.tsx exports its props interface
interface YakuakeTerminalProps {
  show: boolean;
  toggleShow: () => void;
}

const YakuakeTerminal = dynamic<YakuakeTerminalProps>(
  () => import('../components/YakuakeTerminal.tsx').then(mod => mod.default),
  {
    ssr: false, // Disable server-side rendering for this component
    loading: () => <p>Loading Terminal...</p>, // Optional loading component
  }
);

// export const metadata: Metadata = { // Metadata needs to be handled differently with 'use client'
//   title: "Lugari",
//   description: "Lugari LLM Interaction Panel",
// };
// For 'use client' components, set title in a useEffect or through a different mechanism if needed globally.
// Or, keep this a server component and wrap the part needing client features.
// For simplicity here, I'm making the whole layout client-side.

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const [showTerminal, setShowTerminal] = useState(false);

  const toggleTerminal = () => {
    setShowTerminal(prev => !prev);
  };

  // Optional: Add effect to set document title if metadata object isn't used
  useEffect(() => {
    document.title = "Lugari";
  }, []);

  return (
    <html lang="en">
      <body>
        <div className="lugari-app-container">
          {children}
        </div>
        <YakuakeTerminal show={showTerminal} toggleShow={toggleTerminal} />
      </body>
    </html>
  );
}
// Removed duplicated metadata block that was here
