'use client';

import React, { useState, useEffect, ChangeEvent, FormEvent, useRef } from 'react';
import MarkdownIt from 'markdown-it';
import {
  invoke,
  selectImageFile,
  sendChatWithImage,
  loadApiKeys as tauriLoadApiKeys, // Keep alias if used elsewhere, or remove if direct calls are preferred
  saveApiKeys as tauriSaveApiKeys, // Keep alias if used elsewhere, or remove if direct calls are preferred
  getOpenRouterModels,
  getGoogleGeminiModels,
  getOllamaModels, // Import getOllamaModels directly
  selectCodeEntity,
  CodeFile,
  OllamaMessage as ApiOllamaMessage,
  overwriteExistingFile
} from '../lib/tauri-api.ts';
import SettingsModal from '../components/SettingsModal.tsx';
import ChatSessions from '../components/ChatSessions.tsx';
import CodeEditorDisplay, { CodeEditorDisplayHandles } from '../components/CodeEditorDisplay.tsx';
// import CustomTerminal from '../components/CustomTerminal.tsx'; // Old terminal
// import EnhancedTerminal from '../components/EnhancedTerminal.tsx'; // Old terminal
import hljs from 'highlight.js';
import 'highlight.js/styles/atom-one-dark.css';
import styles from './page.module.css';

  // No longer need local declare global for electronAPI, it's handled in tauri-api.ts
  // Extend the Window interface for Tauri IPC only if still needed for other tauri specific things.
  declare global {
    interface Window {
      __TAURI_INTERNALS__?: {
        invoke: (cmd: string, args?: Record<string, unknown>) => Promise<any>;
      };
    }
  }

interface Message {
  sender: 'user' | 'lugari';
  text: string;
}

type ImageDialogResponseRust = {
  file_path: string | null;
  file_name: string | null;
  mime_type: string | null;
  base64_content: string | null;
  data_url: string | null;
};

interface ApiKeys {
  openrouter_key?: string;
  gemini_key?: string;
  ollama_url?: string;
  lm_studio_url?: string;
}

interface ParseDiffResult {
  updatedContent: string;
  changesAppliedCount: number;
  logMessages: string[];
}

const parseAndApplyDiff = (originalContent: string, diffString: string): ParseDiffResult => {
  const logMessages: string[] = [];
  logMessages.push(`Original content (first 500 chars): ${originalContent.substring(0, 500)}${originalContent.length > 500 ? "..." : ""}`);
  logMessages.push(`Diff string received (first 500 chars): ${diffString.substring(0, 500)}${diffString.length > 500 ? "..." : ""}`);

  // Check if the response contains a complete code block without diff markers
  // This would indicate the LLM regenerated the entire file without using diff format
  const codeBlockRegex = /```(?:[a-zA-Z0-9_.-]+)?\s*\n([\s\S]*?)\n```/;
  const codeBlockMatch = codeBlockRegex.exec(diffString);

  if (codeBlockMatch &&
      !diffString.includes("<<<<<<< SEARCH") &&
      !diffString.includes("=======") &&
      !diffString.includes(">>>>>>> REPLACE")) {
    // LLM provided a complete file replacement instead of diffs
    const completeCode = codeBlockMatch[1].trim();
    logMessages.push("LLM provided a complete file replacement instead of diffs. Using the entire code block.");
    return {
      updatedContent: completeCode,
      changesAppliedCount: 1,
      logMessages
    };
  }

  // Process using diff format
  let currentContent = originalContent;
  const diffPattern = /<<<<<<< SEARCH\n([\s\S]*?)\n=======\n([\s\S]*?)\n>>>>>>> REPLACE/g;
  let match;
  let changesAppliedCount = 0;
  let diffBlockIndex = 0;

  while ((match = diffPattern.exec(diffString)) !== null) {
    diffBlockIndex++;
    const searchBlock = match[1];
    const replaceBlock = match[2];
    logMessages.push(`Processing Diff Block #${diffBlockIndex}:\nSEARCH:\n---\n${searchBlock}\n---\nREPLACE:\n---\n${replaceBlock}\n---`);

    if (searchBlock === null || searchBlock === undefined ) {
      const warnMsg = `Diff Block #${diffBlockIndex}: Empty or undefined SEARCH block found. Skipping this change.`;
      console.warn(warnMsg);
      logMessages.push(`WARN: ${warnMsg}`);
      continue;
    }

    const normalize = (str: string) => str.replace(/\r\n/g, '\n');
    const normalizedSearch = normalize(searchBlock);
    const normalizedCurrentContentForSearch = normalize(currentContent);

    logMessages.push(`Diff Block #${diffBlockIndex}: Normalized SEARCH block:\n---\n${normalizedSearch}\n---`);

    if (normalizedCurrentContentForSearch.includes(normalizedSearch)) {
      logMessages.push(`Diff Block #${diffBlockIndex}: SEARCH block found. Attempting replacement.`);
      currentContent = normalizedCurrentContentForSearch.replace(normalizedSearch, normalize(replaceBlock));
      changesAppliedCount++;
      logMessages.push(`Diff Block #${diffBlockIndex}: Content after this replacement (first 500 chars): ${currentContent.substring(0, 500)}${currentContent.length > 500 ? "..." : ""}`);
    } else {
      const warnMsg = `Diff Block #${diffBlockIndex}: SEARCH block NOT FOUND or did not match exactly. Normalized SEARCH block was:\n---\n${normalizedSearch}\n---`;
      console.warn(warnMsg);
      logMessages.push(`WARN: ${warnMsg}`);
    }
  }

  if (changesAppliedCount > 0) {
    logMessages.push(`Total changes applied: ${changesAppliedCount}`);
    console.log(`Total changes applied: ${changesAppliedCount}`);
  } else {
    const warnMsg = "No changes were applied from the diff string. Original content returned.";
    console.warn(warnMsg);
    logMessages.push(`WARN: ${warnMsg}`);
  }
  return { updatedContent: currentContent, changesAppliedCount, logMessages };
};

// Define a ChatSession interface for the chat sessions sidebar
interface ChatSession {
  id: string;
  title: string;
  timestamp: string;
  updatedAt: string;
  messageCount: number;
  messages: Message[]; // Store the actual messages for each chat session
}

export default function HomePage() {
  const [llmProvider, setLlmProvider] = useState<string>('Ollama');
  const [selectedModel, setSelectedModel] = useState<string>('');
  const [currentMode, setCurrentMode] = useState<'Draft' | 'Implement'>('Draft');
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputText, setInputText] = useState<string>('');
  const [ollamaModels, setOllamaModels] = useState<string[]>([]);
  const [lmStudioModels, setLmStudioModels] = useState<string[]>([]);
  const [openRouterModels, setOpenRouterModels] = useState<string[]>([]);
  const [googleGeminiModels, setGoogleGeminiModels] = useState<string[]>([]);
  const [modelError, setModelError] = useState<string | null>(null);
  // const [isTerminalVisible, setIsTerminalVisible] = useState<boolean>(false); // For old terminal

  // Chat sessions state
  const [isChatSessionsOpen, setIsChatSessionsOpen] = useState<boolean>(false);
  const [chatSessions, setChatSessions] = useState<ChatSession[]>([
    {
      id: '1',
      title: 'Chat 5/19/2025, 9:59:05 AM',
      timestamp: '5/19/2025, 9:59:05 AM',
      updatedAt: '5/19/2025, 9:59:05 AM',
      messageCount: 2,
      messages: [
        { sender: 'user', text: 'Hello, can you help me with some code?' },
        { sender: 'lugari', text: 'Of course! What kind of code are you working on?' }
      ]
    },
    {
      id: '2',
      title: 'Chat 5/19/2025, 9:57:28 AM',
      timestamp: '5/19/2025, 9:57:28 AM',
      updatedAt: '5/19/2025, 9:57:28 AM',
      messageCount: 2,
      messages: [
        { sender: 'user', text: 'How do I create a React component?' },
        { sender: 'lugari', text: 'To create a React component, you can use either a function or a class. Here\'s a simple functional component example:\n\n```jsx\nfunction MyComponent() {\n  return <div>Hello World</div>;\n}\n\nexport default MyComponent;\n```' }
      ]
    },
    {
      id: '3',
      title: 'Chat 5/19/2025, 9:56:13 AM',
      timestamp: '5/19/2025, 9:56:13 AM',
      updatedAt: '5/19/2025, 9:56:13 AM',
      messageCount: 2,
      messages: [
        { sender: 'user', text: 'What is TypeScript?' },
        { sender: 'lugari', text: 'TypeScript is a strongly typed programming language that builds on JavaScript, giving you better tooling at any scale.' }
      ]
    },
    {
      id: '4',
      title: 'Chat 5/19/2025, 9:51:10 AM',
      timestamp: '5/19/2025, 9:51:10 AM',
      updatedAt: '5/19/2025, 9:51:10 AM',
      messageCount: 2,
      messages: [
        { sender: 'user', text: 'How do I use async/await in JavaScript?' },
        { sender: 'lugari', text: 'Async/await is a way to handle asynchronous operations in JavaScript. Here\'s an example:\n\n```javascript\nasync function fetchData() {\n  try {\n    const response = await fetch(\'https://api.example.com/data\');\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error(\'Error fetching data:\', error);\n  }\n}\n```' }
      ]
    }
  ]);
  const [mdParser, setMdParser] = useState<MarkdownIt | null>(null);
  const [selectedImageDataUrl, setSelectedImageDataUrl] = useState<string | null>(null);
  const [selectedImageName, setSelectedImageName] = useState<string | null>(null);
  const [isSettingsModalOpen, setIsSettingsModalOpen] = useState<boolean>(false);
  const [currentApiKeys, setCurrentApiKeys] = useState<ApiKeys>({});
  const [settingsError, setSettingsError] = useState<string | null>(null);
  const [extractedCode, setExtractedCode] = useState<string>('');
  const [extractedLanguage, setExtractedLanguage] = useState<string>('');
  const [suggestedFilename, setSuggestedFilename] = useState<string>('');
  const [llmSuggestedDiff, setLlmSuggestedDiff] = useState<string | null>(null);

  const [activeFilePath, setActiveFilePath] = useState<string | null>(null);
  const [activeFileContent, setActiveFileContent] = useState<string>('');
  const [originalFileContent, setOriginalFileContent] = useState<string>('');
  const [activeFileLanguage, setActiveFileLanguage] = useState<string>('plaintext');

  const [statusMessage, setStatusMessage] = useState<string>('');
  const [debugActiveContentSnapshot, setDebugActiveContentSnapshot] = useState<string>('');
  const [debugLlmDiffSnapshot, setDebugLlmDiffSnapshot] = useState<string>('');
  const [diffApplyDetailMessage, setDiffApplyDetailMessage] = useState<string>('');
  const [ingestedFilePaths, setIngestedFilePaths] = useState<string[]>([]);
  const [activeContextPaths, setActiveContextPaths] = useState<string[]>([]);
  const [verboseIngestionFeedback, setVerboseIngestionFeedback] = useState<boolean>(true);
  const [isCancelling, setIsCancelling] = useState<boolean>(false);
  const [isGeneratingReport, setIsGeneratingReport] = useState<boolean>(false);
  const [isAddingDocs, setIsAddingDocs] = useState<boolean>(false); // New state

  const codeEditorRef = useRef<CodeEditorDisplayHandles>(null); // Ref for CodeEditorDisplay


  const handleApplyLLMSuggestions = () => {
    setDebugActiveContentSnapshot(activeFileContent || "activeFileContent was null/empty at click time");
    setDebugLlmDiffSnapshot(llmSuggestedDiff || "llmSuggestedDiff was null/empty at click time");
    setStatusMessage('');
    setDiffApplyDetailMessage('');

    if (activeFileContent && llmSuggestedDiff) {
      // Check if the LLM response contains a complete code block without diff markers
      const codeBlockRegex = /```(?:[a-zA-Z0-9_.-]+)?\s*\n([\s\S]*?)\n```/;
      const codeBlockMatch = codeBlockRegex.exec(llmSuggestedDiff);

      let updatedContent = '';
      let changesAppliedCount = 0;
      let parseLogMessages: string[] = [];

      if (codeBlockMatch &&
          !llmSuggestedDiff.includes("<<<<<<< SEARCH") &&
          !llmSuggestedDiff.includes("=======") &&
          !llmSuggestedDiff.includes(">>>>>>> REPLACE")) {
        // LLM provided a complete file replacement
        updatedContent = codeBlockMatch[1].trim();
        changesAppliedCount = 1;
        parseLogMessages = ["LLM provided a complete file replacement. Replaced entire file content."];
      } else {
        // Process using diff format
        const result = parseAndApplyDiff(activeFileContent, llmSuggestedDiff);
        updatedContent = result.updatedContent;
        changesAppliedCount = result.changesAppliedCount;
        parseLogMessages = result.logMessages;
      }

      setActiveFileContent(updatedContent);
      setLlmSuggestedDiff(null);

      const applyMessageSummary = `LLM suggestions processed. ${changesAppliedCount} change(s) were applied.`;
      setStatusMessage(applyMessageSummary);

      let detailMessage = applyMessageSummary;
      if (parseLogMessages.length > 0) {
        detailMessage += "\nDetails:\n" + parseLogMessages.join("\n");
      }
      setDiffApplyDetailMessage(detailMessage);
    } else {
      const warningMessage = `Cannot apply suggestions. Condition (activeFileContent && llmSuggestedDiff) was false.`;
      setStatusMessage(warningMessage);
      setDiffApplyDetailMessage(warningMessage + `\nactiveFileContent exists: ${!!activeFileContent}, llmSuggestedDiff exists: ${!!llmSuggestedDiff}`);
    }
  };

  const handleSaveActiveFile = async () => {
    if (!activeFilePath) {
      setStatusMessage("No active file to save.");
      return;
    }
    setStatusMessage("Saving file...");
    try {
      await overwriteExistingFile(activeFilePath, activeFileContent);
      // Update the original content to match the saved content
      setOriginalFileContent(activeFileContent);
      setStatusMessage(`File saved: ${activeFilePath.split(/[/\\]/).pop()}`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      setStatusMessage(`Error saving file: ${errorMessage}`);
    }
  };

  const handleSaveAs = async () => {
    const contentToSave = activeFileContent || extractedCode || "";
    if (!activeFilePath && !extractedCode && contentToSave === "") {
        setStatusMessage("No content to save as new file.");
        return;
    }
    let defaultFilename = "untitled.txt";
    if (activeFilePath) {
        defaultFilename = activeFilePath.split(/[/\\]/).pop() || "untitled.txt";
    } else if (suggestedFilename) {
        defaultFilename = suggestedFilename;
    } else if (extractedLanguage) {
        defaultFilename = `untitled.${extractedLanguage.toLowerCase()}`;
    }
    setStatusMessage("Saving as new file...");
    try {
        const savedFilePath = await invoke<string>('save_new_code_file', {
            filenameSuggestion: defaultFilename,
            content: contentToSave
        });
        setStatusMessage(`File saved as: ${savedFilePath.split(/[/\\]/).pop()}`);
        setActiveFilePath(savedFilePath);
        setActiveFileContent(contentToSave);
        setOriginalFileContent(contentToSave); // Store original content for change detection
        const newExtension = savedFilePath.split('.').pop()?.toLowerCase() || '';
        const newLang = newExtension === 'js' ? 'javascript' :
                        newExtension === 'ts' || newExtension === 'tsx' ? 'typescript' :
                        newExtension === 'py' ? 'python' :
                        newExtension === 'rs' ? 'rust' :
                        newExtension === 'html' ? 'html' :
                        newExtension === 'css' ? 'css' :
                        newExtension === 'json' ? 'json' :
                        newExtension === 'md' ? 'markdown' :
                        'plaintext';
        setActiveFileLanguage(newLang);
        setExtractedCode('');
        setExtractedLanguage('');
        setSuggestedFilename('');
        setIngestedFilePaths([]);
        setActiveContextPaths([]);
    } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        setStatusMessage(`Error saving file as: ${errorMessage}`);
    }
  };

  const handleUploadCode = async () => {
    if (!selectedModel) {
      setModelError('Please select a model before uploading code.');
      return;
    }
    setModelError(null);
    setStatusMessage("Selecting code file(s)/folder...");
    setDebugActiveContentSnapshot('');
    setDebugLlmDiffSnapshot('');
    setDiffApplyDetailMessage('');
    setIsCancelling(false);

    try {
      const filesToUpload: CodeFile[] = await selectCodeEntity();
      if (isCancelling) { setStatusMessage("Code upload cancelled by user."); setIsCancelling(false); return; }

      if (!filesToUpload || filesToUpload.length === 0) {
        setStatusMessage("Code upload cancelled or no files selected/found.");
        return;
      }
      setStatusMessage(`Preparing to upload ${filesToUpload.length} file(s)...`);

      const initialUserMessageText = verboseIngestionFeedback
        ? `Please analyze and "memorize" the following code file(s). Become intimate with this code, remember where each function is, what it does, and how it ties into other functions. The goal is for you to assist with future troubleshooting, debugging, and code creation for this project. I will send each file one by one. Please acknowledge each file after you have processed it.`
        : `I am uploading ${filesToUpload.length} file(s) for you to analyze and "memorize" for context. Please process them silently. I will ask for a single confirmation when all files have been sent.`;
      const userUploadMessage: Message = { sender: 'user', text: initialUserMessageText };
      setMessages(prevMessages => [...prevMessages, userUploadMessage]);

      const currentIngestedPaths: string[] = [];

      for (let i = 0; i < filesToUpload.length; i++) {
        if (isCancelling) { setStatusMessage("Code upload cancelled by user."); break; }
        const file = filesToUpload[i];
        setStatusMessage(`Ingesting file ${i + 1} of ${filesToUpload.length}: ${file.path.split(/[/\\]/).pop()}`);

        const ingestionPromptText = `File Path: "${file.path}"
File Content:
\`\`\`
${file.content}
\`\`\`
Instruction: This is file ${i + 1} of ${filesToUpload.length} being uploaded. Please analyze and "memorize" this specific file's content, its functions, and its purpose. ${verboseIngestionFeedback ? "Acknowledge receipt and confirm you have processed this file." : "Process this file silently for your context; no individual acknowledgment needed for this file."}`;

        let ollamaMessageForThisFile: ApiOllamaMessage[] | undefined = undefined;
        if (llmProvider === 'Ollama') {
          ollamaMessageForThisFile = [{role: 'user', content: ingestionPromptText}];
        }

        try {
          const llmResponse = await sendChatWithImage({
            provider: llmProvider,
            modelName: selectedModel,
            promptText: llmProvider === 'Ollama' ? JSON.stringify(ollamaMessageForThisFile) : ingestionPromptText,
            messages: llmProvider === 'Ollama' ? ollamaMessageForThisFile : undefined,
          });

          if (isCancelling) { setStatusMessage("Code upload cancelled by user."); break; }
          const cleanedLlmResponse = llmResponse.replace(/<\/?think>/g, "").trim();

          if (verboseIngestionFeedback) {
            const lugariAckMessage: Message = { sender: 'lugari', text: cleanedLlmResponse };
            setMessages(prevMessages => [...prevMessages, lugariAckMessage]);
          }
          console.log(`LLM response for ${file.path}:`, cleanedLlmResponse);
          currentIngestedPaths.push(file.path);
        } catch (err) {
          if (isCancelling) { setStatusMessage("Code upload cancelled by user."); break; }
          const errorMsg = err instanceof Error ? err.message : String(err);
          const lugariErrMsg: Message = { sender: 'lugari', text: `Error processing ${file.path}: ${errorMsg}` };
          setMessages(prevMessages => [...prevMessages, lugariErrMsg]);
          setStatusMessage(`Error ingesting ${file.path}.`);
        }
      }

      setIngestedFilePaths(prev => {
        const combined = [...prev, ...currentIngestedPaths];
        return [...new Set(combined)]; // Ensure uniqueness
      });
      setActiveContextPaths(prev => {
        const combined = [...prev, ...currentIngestedPaths];
        return [...new Set(combined)]; // Ensure uniqueness and all newly ingested are active
      });

      if (!isCancelling) {
        const totalIngested = ingestedFilePaths.length + currentIngestedPaths.filter(p => !ingestedFilePaths.includes(p)).length; // Recalculate for accurate total
        setStatusMessage(`${currentIngestedPaths.length} new file(s) processed. Total ${totalIngested} file(s) in context.`);
        if (!verboseIngestionFeedback && currentIngestedPaths.length > 0) {
          const finalConfirmationPromptText = `All ${currentIngestedPaths.length} files have been sent for silent ingestion. Please respond with only "Context updated and ready." if you have processed them and are prepared.`;
          const userFinalMessage: Message = { sender: 'user', text: finalConfirmationPromptText };
          setMessages(prevMessages => [...prevMessages, userFinalMessage]);

          let finalOllamaMessages: ApiOllamaMessage[] | undefined = undefined;
          if (llmProvider === 'Ollama') {
             finalOllamaMessages = [{role: 'user', content: finalConfirmationPromptText}];
          }

          try {
            const llmFinalResponse = await sendChatWithImage({
              provider: llmProvider,
              modelName: selectedModel,
              promptText: llmProvider === 'Ollama' ? JSON.stringify(finalOllamaMessages) : finalConfirmationPromptText,
              messages: llmProvider === 'Ollama' ? finalOllamaMessages : undefined,
            });
            const cleanedFinalResponse = llmFinalResponse.replace(/<\/?think>/g, "").trim();
            const lugariFinalMessage: Message = { sender: 'lugari', text: cleanedFinalResponse };
            setMessages(prevMessages => [...prevMessages, lugariFinalMessage]);
          } catch (err) {
            const errorMsg = err instanceof Error ? err.message : String(err);
            console.error("Error sending final confirmation for silent upload:", errorMsg);
            const lugariErrMsg: Message = { sender: 'lugari', text: `Error getting final confirmation: ${errorMsg}` };
            setMessages(prevMessages => [...prevMessages, lugariErrMsg]);
            setStatusMessage(`Error getting final confirmation after silent upload.`);
          }
        }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      setStatusMessage(`Code upload failed: ${errorMessage}`);
      setModelError(`Code upload failed: ${errorMessage}`);
    } finally {
      setIsCancelling(false);
    }
  };

  const handleGenerateReport = async () => {
    if (activeContextPaths.length === 0) {
      setStatusMessage("No active code context to generate a report. Please use 'Upload Code' and select context files.");
      return;
    }
    if (!selectedModel) {
      setModelError('Please select a model before generating a report.');
      return;
    }
    setModelError(null);
    setStatusMessage("Generating code report...");
    setIsCancelling(false);
    setIsGeneratingReport(true);

    const reportPromptText = `Based on the codebase context you have received (consisting of ${activeContextPaths.length} active file(s): ${activeContextPaths.map(p => p.split(/[/\\]/).pop()).join(', ')}), please generate a comprehensive report.
Include the following sections if possible:
1.  High-level summary of the codebase.
2.  Identification of main components/modules and their purposes.
3.  Observed coding patterns or technologies used.
4.  Potential areas for improvement, bugs, or code smells.
5.  An outline for potential documentation.

Please provide a detailed and structured report.`;

    const userReportRequestMessage: Message = { sender: 'user', text: `Please generate a report for the currently active codebase context (${activeContextPaths.length} file(s)).` };
    setMessages(prevMessages => [...prevMessages, userReportRequestMessage]);

    let ollamaMessagesForReport: ApiOllamaMessage[] | undefined = undefined;
    if (llmProvider === 'Ollama') {
      ollamaMessagesForReport = messages.map(msg => ({ // Use current messages state for history
        role: msg.sender === 'lugari' ? 'assistant' : msg.sender,
        content: msg.text,
      }));
      ollamaMessagesForReport.push({ role: 'user', content: reportPromptText });
    }

    try {
      const llmResponse = await sendChatWithImage({
        provider: llmProvider,
        modelName: selectedModel,
        promptText: llmProvider === 'Ollama' ? JSON.stringify(ollamaMessagesForReport) : reportPromptText,
        messages: ollamaMessagesForReport,
      });
      if (isCancelling) { setStatusMessage("Report generation cancelled."); return; }

      const cleanedLlmResponse = llmResponse.replace(/<\/?think>/g, "").trim();
      const lugariReportMessage: Message = { sender: 'lugari', text: cleanedLlmResponse };
      setMessages(prevMessages => [...prevMessages, lugariReportMessage]);
      setStatusMessage("Code report generated.");
    } catch (err) {
      if (isCancelling) { setStatusMessage("Report generation cancelled."); return; }
      const errorMsg = err instanceof Error ? err.message : String(err);
      console.error("Error generating code report:", errorMsg);
      const lugariErrMsg: Message = { sender: 'lugari', text: `Error generating report: ${errorMsg}` };
      setMessages(prevMessages => [...prevMessages, lugariErrMsg]);
      setStatusMessage(`Error generating report.`);
      setModelError(`Error generating report: ${errorMsg}`);
    } finally {
      setIsCancelling(false);
      setIsGeneratingReport(false);
    }
  };

  const handleAddDocsToSelection = async () => {
    if (!selectedModel) {
      setModelError('Please select a model.');
      return;
    }
    const selectedText = codeEditorRef.current?.getSelectedText();
    if (!selectedText) {
      setStatusMessage("No code selected in the editor to add documentation.");
      return;
    }

    setModelError(null);
    setStatusMessage("Generating documentation for selected code...");
    setIsAddingDocs(true);
    setIsCancelling(false);

    const currentLang = activeFilePath ? activeFileLanguage : extractedLanguage || 'plaintext';
    const docsPromptText = `Please generate appropriate comments and/or docstrings for the following ${currentLang} code snippet.
Return only the original code snippet with the new documentation directly inserted where it belongs. Ensure the output is a single code block.

Original Code Snippet:
\`\`\`${currentLang}
${selectedText}
\`\`\`

Modified Code Snippet with Documentation:`;

    const userDocsRequestMessage: Message = { sender: 'user', text: `Add documentation to the selected ${currentLang} code.` };
    setMessages(prev => [...prev, userDocsRequestMessage]);

    let ollamaMessagesForDocs: ApiOllamaMessage[] | undefined = undefined;
    if (llmProvider === 'Ollama') {
      ollamaMessagesForDocs = messages.map(msg => ({ // Use current messages state for history
        role: msg.sender === 'lugari' ? 'assistant' : msg.sender,
        content: msg.text,
      }));
      ollamaMessagesForDocs.push({ role: 'user', content: docsPromptText });
    }

    try {
      const llmResponse = await sendChatWithImage({
        provider: llmProvider,
        modelName: selectedModel,
        promptText: llmProvider === 'Ollama' ? JSON.stringify(ollamaMessagesForDocs) : docsPromptText,
        messages: ollamaMessagesForDocs,
      });

      if (isCancelling) { setStatusMessage("Documentation generation cancelled."); return; }

      const cleanedLlmResponse = llmResponse.replace(/<\/?think>/g, "").trim();
      // We expect the LLM to return the modified code block.
      // For now, display it in chat. User can copy-paste.
      const lugariDocsMessage: Message = { sender: 'lugari', text: cleanedLlmResponse };
      setMessages(prev => [...prev, lugariDocsMessage]);
      setStatusMessage("Documentation generated for selected code.");

    } catch (err) {
      if (isCancelling) { setStatusMessage("Documentation generation cancelled."); return; }
      const errorMsg = err instanceof Error ? err.message : String(err);
      console.error("Error generating documentation:", errorMsg);
      const lugariErrMsg: Message = { sender: 'lugari', text: `Error generating documentation: ${errorMsg}` };
      setMessages(prev => [...prev, lugariErrMsg]);
      setStatusMessage(`Error generating documentation.`);
      setModelError(`Error generating documentation: ${errorMsg}`);
    } finally {
      setIsCancelling(false);
      setIsAddingDocs(false);
    }
  };

  const handleStop = () => {
    setIsCancelling(true);
    setStatusMessage("Stop request received. Attempting to cancel current operation...");
    if (isGeneratingReport) setIsGeneratingReport(false);
    if (isAddingDocs) setIsAddingDocs(false);
    console.log("Stop button clicked, isCancelling set to true.");
  };

  const handleToggleActiveContextPath = (path: string) => {
    setActiveContextPaths(prev =>
      prev.includes(path) ? prev.filter(p => p !== path) : [...prev, path]
    );
  };

  useEffect(() => {
    const md = new MarkdownIt({
      html: true,
      linkify: true,
      typographer: true,
      highlight: function (str, lang) {
        if (lang && hljs.getLanguage(lang)) {
          try {
            return hljs.highlight(str, { language: lang, ignoreIllegals: true }).value;
          } catch {
            // ignore error
          }
        }
        return '';
      }
    });
    setMdParser(md);
    // Debounce or ensure this only runs once effectively if provider hasn't changed
    // For now, it runs on llmProvider change, which is fine.
    initializeModels(llmProvider);
  }, [llmProvider]);

  // No need for a second identical useEffect for llmProvider

  const waitForIpc = async (maxAttempts = 25, interval = 200) => { // Poll for up to 5 seconds
    if (window.electronAPI) {
      console.log("Running in Electron environment. Tauri IPC will not be used here.");
      return false; // Indicate Tauri IPC is not available/applicable
    }
    // Proceed with Tauri IPC check if not in Electron
    for (let i = 0; i < maxAttempts; i++) {
      if (window.__TAURI_INTERNALS__?.invoke) {
        console.log("Tauri IPC is ready.");
        return true;
      }
      console.log(`Tauri IPC not ready, attempt ${i + 1}/${maxAttempts}. Retrying in ${interval}ms...`);
      await new Promise(resolve => setTimeout(resolve, interval));
    }
    console.error("Tauri IPC not available after multiple attempts.");
    return false;
  };

  const initializeModels = async (provider: string) => {
    if (window.electronAPI) {
      // Running in Electron, directly try to fetch models
      console.log("Electron environment detected in initializeModels. Fetching models for provider:", provider);
      fetchModelsForProvider(provider);
    } else {
      // Fallback to Tauri IPC logic
      console.log("Tauri environment detected or Electron API not found. Attempting Tauri IPC for models.");
      const ipcReady = await waitForIpc();
      if (!ipcReady) {
        setModelError("Tauri IPC not ready. Cannot fetch models.");
        setOllamaModels([]);
        setLmStudioModels([]);
        setOpenRouterModels([]);
        setGoogleGeminiModels([]);
        return;
      }
      fetchModelsForProvider(provider);
    }
  };

  const fetchModelsForProvider = async (provider: string) => {
    setModelError(null);
    setSelectedModel('');
    try {
      console.log(`Fetching models for provider: ${provider}`);
      if (provider === 'Ollama') {
        const models = await getOllamaModels(); // Use the direct, migrated function
        console.log('Ollama models (fetched via Electron IPC if available):', models);
        setOllamaModels(models || []);
        setLmStudioModels([]);
        setOpenRouterModels([]);
        setGoogleGeminiModels([]);
      } else if (provider === 'LM Studio') {
        const models = await invoke<string[]>('get_lm_studio_models'); // This will need migration next
        console.log('LM Studio models:', models);
        setLmStudioModels(models || []);
        setOllamaModels([]);
        setOpenRouterModels([]);
        setGoogleGeminiModels([]);
      } else if (provider === 'OpenRouter') {
        const models = await getOpenRouterModels(); // This already calls invoke internally
        console.log('OpenRouter models:', models);
        setOpenRouterModels(models || []);
        setOllamaModels([]);
        setLmStudioModels([]);
        setGoogleGeminiModels([]);
      } else if (provider === 'Google Gemini') {
        const models = await getGoogleGeminiModels(); // This also calls invoke internally
        console.log('Google Gemini models:', models);
        setGoogleGeminiModels(models || []);
        setOllamaModels([]);
        setLmStudioModels([]);
        setOpenRouterModels([]);
      } else {
        setOllamaModels([]);
        setLmStudioModels([]);
        setOpenRouterModels([]);
        setGoogleGeminiModels([]);
      }
    } catch (error) {
      console.error(`Error fetching models for ${provider}:`, error);
      setModelError(typeof error === 'string' ? error : `Failed to fetch models for ${provider}.`);
      // Clear specific provider models on error
      if (provider === 'Ollama') setOllamaModels([]);
      if (provider === 'LM Studio') setLmStudioModels([]);
      if (provider === 'OpenRouter') setOpenRouterModels([]);
      if (provider === 'Google Gemini') setGoogleGeminiModels([]);
    }
  };

  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      const message = event.data;
      switch (message.command) {
        case 'addMessage':
          setMessages(prevMessages => [...prevMessages, message.payload]);
          break;
      }
    };
    window.addEventListener('message', handleMessage);
    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, []);

  // Handle keyboard shortcut (F12) to toggle terminal - This is now handled by YakuakeTerminal itself
  // useEffect(() => {
  //   const handleKeyDown = (e: KeyboardEvent) => {
  //     if (e.key === 'F12') {
  //       e.preventDefault();
  //       // setIsTerminalVisible(prev => !prev); // This controlled the old EnhancedTerminal
  //     }
  //   };

  //   window.addEventListener('keydown', handleKeyDown);
  //   return () => {
  //     window.removeEventListener('keydown', handleKeyDown);
  //   };
  // }, []);


  const handleProviderChange = (event: ChangeEvent<HTMLSelectElement>) => {
    const newProvider = event.target.value;
    setLlmProvider(newProvider);
  };

  const handleModelChange = (event: ChangeEvent<HTMLSelectElement>) => {
    setSelectedModel(event.target.value);
  };

  const handleModeToggle = (mode: 'Draft' | 'Implement') => {
    setCurrentMode(mode);
    console.log("Mode changed to:", mode);
  };

  const handleClearChat = () => {
    setMessages([]);
    setSelectedImageDataUrl(null);
    setSelectedImageName(null);
    setExtractedCode('');
    setExtractedLanguage('');
    setSuggestedFilename('');
    setLlmSuggestedDiff(null);
    setDebugActiveContentSnapshot('');
    setDebugLlmDiffSnapshot('');
    setStatusMessage('');
    setDiffApplyDetailMessage('');
    setIngestedFilePaths([]);
    setActiveContextPaths([]);
    setIsCancelling(false);
    setIsGeneratingReport(false);
    setIsAddingDocs(false);
  };

  // Chat sessions handlers
  const handleToggleChatSessions = () => {
    setIsChatSessionsOpen(!isChatSessionsOpen);
  };

  const handleCreateNewChat = (title: string, preserveContext: boolean) => {
    const now = new Date();
    const timestamp = now.toLocaleString();
    const newChat: ChatSession = {
      id: Date.now().toString(),
      title: title || `Chat ${timestamp}`,
      timestamp,
      updatedAt: timestamp,
      messageCount: 0,
      messages: [] // Initialize with empty messages array
    };

    setChatSessions([newChat, ...chatSessions]);

    if (!preserveContext) {
      handleClearChat();
    }

    setIsChatSessionsOpen(false);
  };

  const handleSelectChat = (sessionId: string) => {
    // Find the selected chat session
    const selectedSession = chatSessions.find(session => session.id === sessionId);
    if (selectedSession) {
      // Load the selected chat's messages
      setMessages(selectedSession.messages);
      console.log(`Loaded chat session: ${selectedSession.title} with ${selectedSession.messages.length} messages`);

      // Clear any active file or extracted code
      setActiveFilePath('');
      setActiveFileContent('');
      setActiveFileLanguage('');
      setExtractedCode('');
      setExtractedLanguage('');
      setSuggestedFilename('');
      setLlmSuggestedDiff(null);
    }
    setIsChatSessionsOpen(false);
  };

  const handleDeleteChat = (sessionId: string) => {
    setChatSessions(prevSessions => prevSessions.filter(session => session.id !== sessionId));
    console.log(`Deleted chat session: ${sessionId}`);
  };

  const handleDownloadChat = (sessionId: string) => {
    const session = chatSessions.find(s => s.id === sessionId);
    if (session && session.messages.length > 0) {
      console.log(`Downloading chat session: ${session.title}`);

      // Format the chat content
      const chatContent = session.messages.map(msg => {
        return `${msg.sender === 'lugari' ? 'Lugari' : 'You'}: ${msg.text}`;
      }).join('\n\n');

      // Create a blob with the chat content
      const blob = new Blob([chatContent], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);

      // Create a temporary link element to trigger the download
      const a = document.createElement('a');
      a.href = url;
      a.download = `${session.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.txt`;
      document.body.appendChild(a);
      a.click();

      // Clean up
      setTimeout(() => {
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }, 100);

      setStatusMessage(`Downloaded chat: ${session.title}`);
    } else {
      setStatusMessage("No messages to download");
    }
  };

  const handleShareChat = (sessionId: string) => {
    const session = chatSessions.find(s => s.id === sessionId);
    if (session && session.messages.length > 0) {
      console.log(`Sharing chat session: ${session.title}`);

      // Format the chat content for sharing
      const chatContent = session.messages.map(msg => {
        return `${msg.sender === 'lugari' ? 'Lugari' : 'You'}: ${msg.text}`;
      }).join('\n\n');

      // Use the Web Share API if available
      if (navigator.share) {
        navigator.share({
          title: session.title,
          text: chatContent,
        })
        .then(() => setStatusMessage('Chat shared successfully'))
        .catch((error) => {
          console.error('Error sharing chat:', error);
          setStatusMessage('Error sharing chat');
          // Fallback to clipboard if sharing fails
          copyToClipboard(chatContent);
        });
      } else {
        // Fallback for browsers that don't support the Web Share API
        copyToClipboard(chatContent);
      }
    } else {
      setStatusMessage("No messages to share");
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
      .then(() => {
        setStatusMessage('Chat copied to clipboard');
      })
      .catch(err => {
        console.error('Failed to copy chat to clipboard:', err);
        setStatusMessage('Failed to copy chat to clipboard');
      });
  };

  const handleNewChat = () => {
    handleCreateNewChat("", false);
  };

  const handleNewFile = () => {
    // Check if there are unsaved changes in the current file
    if (activeFilePath && activeFileContent !== originalFileContent) {
      if (window.confirm("You have unsaved changes. Do you want to save them before creating a new file?")) {
        handleSaveActiveFile();
      }
    }

    // Create a new empty file
    setActiveFilePath('');
    setActiveFileContent('');
    setActiveFileLanguage('plaintext');
    setExtractedCode('');
    setExtractedLanguage('plaintext');
    setSuggestedFilename('untitled.txt');
    setLlmSuggestedDiff(null);
    setStatusMessage('New file created');
  };

  const handleCloseFile = () => {
    // Check if there are unsaved changes
    if (activeFilePath && activeFileContent !== originalFileContent) {
      if (window.confirm("You have unsaved changes. Do you want to save before closing?")) {
        handleSaveActiveFile();
      }
    }

    // Close the file
    setActiveFilePath(null);
    setActiveFileContent('');
    setOriginalFileContent('');
    setActiveFileLanguage('plaintext');
    setExtractedCode('');
    setExtractedLanguage('');
    setSuggestedFilename('');
    setLlmSuggestedDiff(null);
    setStatusMessage('File closed');
  };

  const handleSaveChat = () => {
    if (messages.length > 0) {
      const now = new Date();
      const timestamp = now.toLocaleString();
      const newChat: ChatSession = {
        id: Date.now().toString(),
        title: `Chat ${timestamp}`,
        timestamp,
        updatedAt: timestamp,
        messageCount: messages.length,
        messages: [...messages] // Save a copy of the current messages
      };

      setChatSessions([newChat, ...chatSessions]);
      setStatusMessage(`Chat saved as: ${newChat.title}`);
    } else {
      setStatusMessage("No messages to save");
    }
  };

  const handleOpenFile = async () => {
    // Check if there are unsaved changes in the current file
    if (activeFilePath && activeFileContent !== originalFileContent) {
      if (!window.confirm("You have unsaved changes. Do you want to discard them and open a new file?")) {
        return; // User cancelled
      }
    }

    try {
      const result = await invoke<{ path: string; content: string; language: string } | null>('open_file_and_read_content');
      if (result) {
        setActiveFilePath(result.path);
        setActiveFileContent(result.content);
        setOriginalFileContent(result.content); // Store original content for change detection
        setActiveFileLanguage(result.language);
        setExtractedCode('');
        setExtractedLanguage('');
        setSuggestedFilename('');
        setLlmSuggestedDiff(null);
        setModelError(null);
        setStatusMessage(`Opened file: ${result.path.split(/[/\\]/).pop()}`);
        setDebugActiveContentSnapshot('');
        setDebugLlmDiffSnapshot('');
        setDiffApplyDetailMessage('');
        setIngestedFilePaths([]);
        setActiveContextPaths([]);
        setIsCancelling(false);
        setIsGeneratingReport(false);
        setIsAddingDocs(false);
      } else {
        setModelError("File open cancelled or no file selected.");
        setStatusMessage('');
      }
    } catch (err) {
      console.error("Error opening file:", err);
      setModelError(err instanceof Error ? err.message : String(err));
      setActiveFilePath(null);
      setActiveFileContent('');
      setOriginalFileContent('');
      setActiveFileLanguage('plaintext');
      setStatusMessage(`Error opening file: ${err instanceof Error ? err.message : String(err)}`);
    }
  };

  const handleOpenSettingsModal = async () => {
    setSettingsError(null);
    try {
      const keys = await tauriLoadApiKeys();
      setCurrentApiKeys(keys || {});
      setIsSettingsModalOpen(true);
    } catch (error) {
      console.error("Error loading API keys:", error);
      setSettingsError(error instanceof Error ? error.message : String(error));
      setCurrentApiKeys({});
      setIsSettingsModalOpen(true);
    }
  };

  const handleCloseSettingsModal = () => {
    setIsSettingsModalOpen(false);
    setSettingsError(null);
  };

  const handleSaveApiKeysInModal = async (keysToSave: ApiKeys): Promise<void> => {
    await tauriSaveApiKeys(keysToSave);
    setCurrentApiKeys(keysToSave);
  };

  const handleImageUpload = async () => {
    try {
      const result: ImageDialogResponseRust | null = await selectImageFile();
      if (result && result.data_url && result.file_name) {
        setSelectedImageDataUrl(result.data_url);
        setSelectedImageName(result.file_name);
        setModelError(null);
        setStatusMessage(`Image selected: ${result.file_name}`);
      } else {
        setSelectedImageDataUrl(null);
        setSelectedImageName(null);
        setStatusMessage('Image selection cancelled.');
      }
    } catch (err) {
      console.error("Error during image selection process:", err);
      setModelError("Failed to load image. " + (err instanceof Error ? err.message : String(err)));
      setSelectedImageDataUrl(null);
      setSelectedImageName(null);
      setStatusMessage(`Failed to load image: ${err instanceof Error ? err.message : String(err)}`);
    }
  };

  const handleSendMessage = async (event: FormEvent) => {
    event.preventDefault();
    if (inputText.trim() === '' || !selectedModel) {
      if (!selectedModel) {
        setModelError('Please select a model before sending a message.');
      }
      return;
    }
    setModelError(null);
    setStatusMessage('');
    setDebugActiveContentSnapshot('');
    setDebugLlmDiffSnapshot('');
    setDiffApplyDetailMessage('');
    setIsCancelling(false);

    const newUserMessage: Message = { sender: 'user', text: inputText };
    const updatedMessagesForUi = [...messages, newUserMessage];
    setMessages(updatedMessagesForUi);

    setExtractedCode('');
    setExtractedLanguage('');
    setSuggestedFilename('');
    setLlmSuggestedDiff(null);

    let finalPromptTextForBackend = "";
    let ollamaMessagesForBackend: ApiOllamaMessage[] | undefined = undefined;

    if (llmProvider === 'Ollama') {
      // If there's an active file, add it as context
      if (activeFilePath && activeFileContent) {
        const contextMessage = `Current file being edited: ${activeFilePath}\n\n\`\`\`${activeFileLanguage}\n${activeFileContent}\n\`\`\`\n\nBased on the user's request, please regenerate the ENTIRE file with the requested modifications. When providing your response, include the complete file content with all modifications applied. Present your response in the following diff format to indicate what changes you made:\n\n<<<<<<< SEARCH\n[exact lines from the current code to be replaced]\n=======\n[new lines to replace the search block with]\n>>>>>>> REPLACE\n\nThe SEARCH block must be an exact character-for-character match of the segment in the current code, including all whitespace and newlines.`;

        // Add the code context as a system message (silent - not shown in UI)
        ollamaMessagesForBackend = [
          { role: 'system', content: contextMessage },
          ...updatedMessagesForUi.map(msg => ({
            role: msg.sender === 'lugari' ? 'assistant' : 'user' as 'system' | 'user' | 'assistant',
            content: msg.text,
          }))
        ];
      } else {
        ollamaMessagesForBackend = updatedMessagesForUi.map(msg => ({
          role: msg.sender === 'lugari' ? 'assistant' : 'user' as 'system' | 'user' | 'assistant',
          content: msg.text,
        }));
      }

      if (selectedImageDataUrl && ollamaMessagesForBackend.length > 0) {
        // Find the last user message to attach the image
        const lastUserMessageIndex = ollamaMessagesForBackend
          .map((msg, index) => ({ role: msg.role, index }))
          .filter(item => item.role === 'user')
          .pop()?.index;

        if (lastUserMessageIndex !== undefined) {
          const base64Part = selectedImageDataUrl.split(',')[1];
          if (base64Part) {
            ollamaMessagesForBackend[lastUserMessageIndex].images =
              [...(ollamaMessagesForBackend[lastUserMessageIndex].images || []), base64Part];
          }
        }
      }
      finalPromptTextForBackend = JSON.stringify(ollamaMessagesForBackend);

    } else {
      // For non-Ollama providers
      if (activeFilePath && activeFileContent) {
        // Include the file content as context
        finalPromptTextForBackend = `System: You are helping the user modify code. The user is currently editing the file: "${activeFilePath}"
Its current content is:
\`\`\`${activeFileLanguage}
${activeFileContent}
\`\`\`

User's request: "${inputText}"

Instruction: Based on the user's request, please regenerate the ENTIRE file with the requested modifications.
When providing your response, include the complete file content with all modifications applied.
Present your response in the following diff format to indicate what changes you made. Use one or more blocks as needed:
<<<<<<< SEARCH
[exact lines from the current code to be replaced]
=======
[new lines to replace the search block with]
>>>>>>> REPLACE
It is CRITICAL that the \`=======\` separator is on its own line between the SEARCH and REPLACE blocks.
The SEARCH block must be an *exact* character-for-character match of the segment in the current code, including all whitespace and newlines.
Do not include any other explanatory text or your thought process outside the diff markers if providing a diff.
---`;
      } else {
        finalPromptTextForBackend = `User's request: "${inputText}"

Instruction: Please respond to the user's request.
If the request implies generating new code, provide the complete code block.
Do not provide a review of the user's input unless explicitly asked to do so.
If generating code, suggest a filename if appropriate.
${activeContextPaths.length > 0 ? `\n\nConsider the following files from the previously uploaded codebase context if relevant: ${activeContextPaths.map(p=>p.split(/[/\\]/).pop()).join(', ')}` : ''}
---`;
      }
    }

    try {
      const responseText = await sendChatWithImage({
        provider: llmProvider,
        modelName: selectedModel,
        promptText: finalPromptTextForBackend,
        messages: llmProvider === 'Ollama' ? ollamaMessagesForBackend : undefined,
        imageDataUrl: llmProvider !== 'Ollama' ? selectedImageDataUrl : undefined,
      });
      if (isCancelling) { setStatusMessage("Message sending cancelled."); setIsCancelling(false); return; }

      let processedResponseText = responseText;
      processedResponseText = processedResponseText.replace(/<\/?think>/g, "").trim();

      // Check if this is a code modification response
      const diffMarkerSearch = "<<<<<<< SEARCH";
      const diffMarkerEquals = "=======";
      const diffMarkerReplace = ">>>>>>> REPLACE";
      const isCodeModification = activeFilePath &&
        (processedResponseText.includes(diffMarkerSearch) ||
         processedResponseText.includes(diffMarkerEquals) ||
         processedResponseText.includes(diffMarkerReplace));

      // If it's a code modification, extract any explanatory text before the diff/code
      let explanatoryText = processedResponseText;
      if (isCodeModification) {
        // Extract any text before the first code block or diff marker
        const firstCodeBlockIndex = processedResponseText.indexOf("```");
        const firstDiffMarkerIndex = processedResponseText.indexOf(diffMarkerSearch);

        let cutoffIndex = processedResponseText.length;
        if (firstCodeBlockIndex !== -1) {
          cutoffIndex = Math.min(cutoffIndex, firstCodeBlockIndex);
        }
        if (firstDiffMarkerIndex !== -1) {
          cutoffIndex = Math.min(cutoffIndex, firstDiffMarkerIndex);
        }

        explanatoryText = processedResponseText.substring(0, cutoffIndex).trim();

        // If there's no explanatory text, provide a default message
        if (!explanatoryText) {
          explanatoryText = "I've analyzed your code modification request. Click 'Apply LLM Suggestions' to implement the changes.";
        }
      }

      // Add only the explanatory text to the chat (not the entire code/diff)
      const lugariMessage: Message = {
        sender: 'lugari',
        text: isCodeModification ? explanatoryText : processedResponseText
      };
      setMessages(prev => [...prev, lugariMessage]);

      console.log("LLM Response Text (raw):", responseText);
      console.log("LLM Response Text (processed):", processedResponseText);

      // Check if this is a code modification for an active file
      if (activeFilePath) {
        // If it contains diff markers, treat it as a diff
        if (processedResponseText.includes(diffMarkerSearch) &&
            processedResponseText.includes(diffMarkerEquals) &&
            processedResponseText.includes(diffMarkerReplace)) {
          setLlmSuggestedDiff(processedResponseText);
          console.log("LLM suggested a diff.");
        }
        // If it contains a code block but no diff markers, it might be a complete file replacement
        else {
          const codeBlockRegex = /```(?:([a-zA-Z0-9_.-]+)\s*\n)?([\s\S]*?)\n```/;
          const codeMatch = codeBlockRegex.exec(processedResponseText);

          if (codeMatch && isCodeModification) {
            // Treat this as a complete file replacement suggestion
            setLlmSuggestedDiff(processedResponseText);
            console.log("LLM suggested a complete file replacement.");
          } else {
            // Process as a normal code block extraction
            processCodeBlock(processedResponseText);
          }
        }
      } else {
        // Not modifying an active file, just extract code if present
        processCodeBlock(processedResponseText);
      }

      function processCodeBlock(text: string) {
        const codeBlockRegex = /```(?:([a-zA-Z0-9_.-]+)\s*\n)?([\s\S]*?)\n```/;
        const codeMatch = codeBlockRegex.exec(text);

        if (codeMatch) {
          const language = codeMatch[1] || 'plaintext';
          let code = codeMatch[2].trim();
          code = code.replace(/<\/?think>/g, "").trim();

          setExtractedLanguage(language);
          setExtractedCode(code);
          console.log("Extracted Language:", language);
          console.log("Extracted Code:", code);

          const textBeforeCodeBlock = text.substring(0, codeMatch.index);
          const filenameRegex = /(?:File|Filename):\s*`?([a-zA-Z0-9_.-]+\.[a-zA-Z0-9]+)`?|named\s*\`?([a-zA-Z0-9_.-]+\.[a-zA-Z0-9]+)\`?|as\s*\`?([a-zA-Z0-9_.-]+\.[a-zA-Z0-9]+)\`?/i;
          const filenameMatch = filenameRegex.exec(textBeforeCodeBlock);
          let foundFilename = '';
          if (filenameMatch) {
            foundFilename = filenameMatch[1] || filenameMatch[2] || filenameMatch[3] || '';
          }
          if (foundFilename) {
            setSuggestedFilename(foundFilename);
            console.log("Suggested Filename:", foundFilename);
          } else {
            console.log("No specific filename suggestion found.");
          }
        } else {
          console.log("No code block or diff found in LLM response.");
        }
      }
    } catch (err: unknown) {
      if (isCancelling && (err as Error).name === 'AbortError') {
        setStatusMessage("Message sending cancelled by user.");
      } else {
        console.error("Error sending message:", err);
        setModelError(err instanceof Error ? err.message : typeof err === 'string' ? err : 'Failed to send message or get response.');
      }
    } finally {
      setIsCancelling(false);
    }

    setInputText('');
    setSelectedImageDataUrl(null);
    setSelectedImageName(null);
  };

  const getModelsForProvider = () => {
    switch (llmProvider) {
      case 'Ollama':
        return ollamaModels;
      case 'LM Studio':
        return lmStudioModels;
      case 'OpenRouter':
        return openRouterModels;
      case 'Google Gemini':
        return googleGeminiModels;
      default: return [];
    }
  };

  const showDebugPanel = !!(debugActiveContentSnapshot || debugLlmDiffSnapshot);

  return (
    <div className={styles.container}>
      <header className={styles.header}>
        <div className={styles.headerLeftControls}>
          <div className={styles.controlsRow}>
            <select value={llmProvider} onChange={handleProviderChange} className={styles.select} aria-label="Select LLM Provider">
              <option value="Ollama">Ollama</option>
              <option value="LM Studio">LM Studio</option>
              <option value="OpenRouter">OpenRouter</option>
              <option value="Google Gemini">Google Gemini</option>
            </select>
            <select value={selectedModel} onChange={handleModelChange} className={styles.select} disabled={!llmProvider || getModelsForProvider().length === 0} aria-label="Select Model">
            <option value="">Select Model</option>
            {getModelsForProvider().map(model => (
              <option key={model} value={model}>{model}</option>
            ))}
          </select>
          </div>
          {modelError && <div className={styles.errorText}>{modelError}</div>}
          {settingsError && <div className={styles.errorText}>Settings Error: {settingsError}</div>}
          {statusMessage && !modelError && !settingsError && <div className={styles.statusMessage}>{statusMessage}</div>}
          {selectedImageName && <div className={styles.imageNameLabel}>Selected: {selectedImageName}</div>}
          {activeFilePath && <div className={styles.filePathLabel}>Editing: {activeFilePath.split(/[/\\]/).pop()}</div>}

          {ingestedFilePaths.length > 0 && (
            <div className={styles.ingestedFilesSection}>
              <div className={styles.ingestedFilesHeader}>
                <span className={styles.ingestedFilesLabel}>Active LLM Context ({activeContextPaths.length} / {ingestedFilePaths.length} file(s)):</span>
                {ingestedFilePaths.length > 1 && (
                  <div className={styles.ingestedFilesControls}>
                    <button type="button" onClick={() => setActiveContextPaths([...ingestedFilePaths])} className={styles.smallButton}>All</button>
                    <button type="button" onClick={() => setActiveContextPaths([])} className={styles.smallButton}>None</button>
                  </div>
                )}
              </div>
              <ul className={styles.ingestedFilesListWithCheckboxes}>
                {ingestedFilePaths.map(path => (
                  <li key={path} title={path}>
                    <input
                      type="checkbox"
                      id={`ctx-${path}`}
                      checked={activeContextPaths.includes(path)}
                      onChange={() => handleToggleActiveContextPath(path)}
                    />
                    <label htmlFor={`ctx-${path}`}>{path.split(/[/\\]/).pop()}</label>
                  </li>
                ))}
              </ul>
            </div>
          )}

          <div className={styles.controlsRow}>
            <button type="button" onClick={handleOpenFile} className={styles.button}>Open File</button>
            <button type="button" onClick={handleNewFile} className={styles.button}>New File</button>
            <button type="button" onClick={handleSaveAs} className={styles.button}>Save As...</button>
            <button type="button" onClick={handleUploadCode} className={styles.button}>Upload Code</button>
            <label htmlFor="verboseIngestion" className={styles.controlsLabel}>
              <input type="checkbox" id="verboseIngestion" checked={verboseIngestionFeedback} onChange={(e) => setVerboseIngestionFeedback(e.target.checked)} />
              Verbose Upload Feedback
            </label>
            <button type="button" onClick={handleImageUpload} className={styles.button}>Upload Images</button>
            <button type="button" onClick={handleOpenSettingsModal} className={styles.button} aria-label="Open API Key Settings">
              Settings
            </button>
          </div>
           {ingestedFilePaths.length > 0 && (
            <div className={styles.controlsRow}>
              <button
                type="button"
                onClick={handleGenerateReport}
                className={styles.button}
                disabled={isGeneratingReport || isCancelling || activeContextPaths.length === 0}
              >
                {isGeneratingReport ? "Generating..." : "Generate Code Report"}
              </button>
            </div>
          )}
        </div>
        <div className={styles.headerRightControls}>
          <div className={styles.modeButtons}>
            <button
              type="button"
              onClick={() => handleModeToggle('Draft')}
              className={`${styles.draftButton} ${currentMode === 'Draft' ? 'active' : ''}`}
            >
              Draft
            </button>
            <button
              type="button"
              onClick={() => handleModeToggle('Implement')}
              className={`${styles.implementButton} ${currentMode === 'Implement' ? 'active' : ''}`}
            >
              Implement
            </button>
          </div>
        </div>
      </header>

      <main className={styles.chatArea}>
        {messages.map((msg, index) => (
          <div key={index} className={`${styles.message} ${styles[msg.sender]}`}>
            <strong>{msg.sender === 'lugari' ? 'Lugari' : 'You'}: </strong>
            {msg.sender === 'lugari' && mdParser
              ? <span dangerouslySetInnerHTML={{ __html: mdParser.render(msg.text) }} />
              : <span>{msg.text}</span>
            }
          </div>
        ))}
        {(extractedCode || activeFilePath) && (
          <div className={styles.codeEditorArea}>
            <h4>
              <span>
                {activeFilePath
                  ? `Editing: ${activeFilePath.split(/[/\\]/).pop()}`
                  : (suggestedFilename ? `Suggested Code for ${suggestedFilename}` : `Suggested Code (New File)`)
                }
              </span>
              {activeFilePath && (
                <button
                  type="button"
                  className={styles.closeFileButton}
                  onClick={handleCloseFile}
                  title="Close file"
                >
                  ×
                </button>
              )}
            </h4>
            <CodeEditorDisplay
              ref={codeEditorRef} // Assign ref here
              code={activeFilePath ? activeFileContent : extractedCode}
              language={activeFilePath ? activeFileLanguage : extractedLanguage}
              height="300px"
              onCodeChange={ activeFilePath ? (newCode: string | undefined) => setActiveFileContent(newCode || '') : undefined }
            />
            <div className={styles.codeEditorButtonsContainer}>
              {activeFilePath && llmSuggestedDiff && (
                <button
                  type="button"
                  onClick={handleApplyLLMSuggestions}
                  className={styles.button}
                >
                  Apply LLM Suggestions
                </button>
              )}
              {(activeFilePath || extractedCode) && ( // Show "Add Docs" if there's code in editor
                <button
                  type="button"
                  onClick={handleAddDocsToSelection}
                  className={styles.button}
                  disabled={isAddingDocs || isCancelling}
                >
                  {isAddingDocs ? "Adding Docs..." : "Add Docs to Selection"}
                </button>
              )}
              {extractedCode && !activeFilePath && (
                <button
                  type="button"
                  onClick={() => handleSaveNewFile(suggestedFilename, extractedCode, extractedLanguage)}
                  className={styles.button}
                >
                  Save {suggestedFilename || `New ${extractedLanguage} File`}
                </button>
              )}
              {activeFilePath && (
                  <button
                      type="button"
                      onClick={handleSaveActiveFile}
                      className={styles.button}
                    >
                      Save Changes to {activeFilePath.split(/[/\\]/).pop()}
                  </button>
              )}
            </div>
          </div>
        )}
      </main>

      <footer className={styles.footer}>
        <form onSubmit={handleSendMessage} className={styles.inputForm}>
          <textarea
            value={inputText}
            onChange={(e) => setInputText(e.target.value)}
            onKeyDown={(e) => {
              // Allow Shift+Enter for new line without submitting
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                handleSendMessage(e);
              }
            }}
            placeholder="Type your message to Lugari... (Shift+Enter for new line)"
            className={`${styles.input} ${currentMode === 'Draft' ? styles.draftHighlightBorder : currentMode === 'Implement' ? styles.implementHighlightBorder : ''}`}
            rows={1}
          />
          <button type="submit" className={styles.iconButton}>Send</button>
          <button type="button" onClick={handleStop} className={styles.iconButton}>Stop</button>
          <button type="button" onClick={handleClearChat} className={styles.iconButton}>Clear</button>
        </form>
      </footer>

      {/* Floating Action Buttons */}
      <div className={styles.floatingButtonsContainer}>
        <button
          type="button"
          className={`${styles.floatingButton} ${isChatSessionsOpen ? styles.active : ''}`}
          onClick={handleToggleChatSessions}
          title="Manage Chats"
        >
          <span className={styles.floatingButtonIcon}>💬</span>
        </button>
        <button
          type="button"
          className={styles.floatingButton}
          onClick={handleNewChat}
          title="New Chat"
        >
          <span className={styles.floatingButtonIcon}>+</span>
        </button>
        <button
          type="button"
          className={styles.floatingButton}
          onClick={handleSaveChat}
          title="Save Chat"
        >
          <span className={styles.floatingButtonIcon}>💾</span>
        </button>
        {/* The terminal icon 📟 used to toggle EnhancedTerminal.
            YakuakeTerminal is toggled by F12 globally from layout.
            If this icon should also toggle YakuakeTerminal, global state management is needed.
            For now, this button will do nothing related to the terminal.
            Alternatively, remove it or repurpose it.
        */}
        <button
          type="button"
          className={`${styles.floatingButton}`} // Removed active state based on isTerminalVisible
          onClick={() => { /* TODO: Implement global state toggle for Yakuake if needed */ alert("Terminal is toggled with F12."); }}
          title="Toggle Terminal (F12)"
        >
          <span className={styles.floatingButtonIcon}>📟</span>
        </button>
      </div>

      {/* Chat Sessions Sidebar */}
      <ChatSessions
        isOpen={isChatSessionsOpen}
        onClose={() => setIsChatSessionsOpen(false)}
        onCreateNewChat={handleCreateNewChat}
        onSelectChat={handleSelectChat}
        onDeleteChat={handleDeleteChat}
        onDownloadChat={handleDownloadChat}
        onShareChat={handleShareChat}
        sessions={chatSessions}
      />

      <SettingsModal
        isOpen={isSettingsModalOpen}
        onClose={handleCloseSettingsModal}
        onSave={handleSaveApiKeysInModal}
        initialKeys={currentApiKeys}
      />

      {/* Terminal Component - Old EnhancedTerminal removed */}
      {/*
      <EnhancedTerminal
        isVisible={isTerminalVisible}
        onClose={() => setIsTerminalVisible(false)}
      />
      */}

      {showDebugPanel && (
        <div className={styles.debugPanel}>
          <h4>Debug Info (Apply LLM Suggestions Snapshot):</h4>
          {diffApplyDetailMessage && (
            <>
              <p><strong>Diff Apply Details:</strong></p>
              <pre
                className={`${styles.debugPre} ${
                  diffApplyDetailMessage.toLowerCase().includes("error") ||
                  diffApplyDetailMessage.toLowerCase().includes("warn:")
                    ? styles.debugPreError
                    : styles.debugPreSuccess
                }`}
              >
                {diffApplyDetailMessage}
              </pre>
            </>
          )}
          <p><strong>Active File Content (Snapshot):</strong></p>
          <textarea
            readOnly
            value={debugActiveContentSnapshot}
            className={styles.debugTextarea}
            aria-label="Active File Content Snapshot"
          />
          <p><strong>LLM Diff (Snapshot):</strong></p>
          <textarea
            readOnly
            value={debugLlmDiffSnapshot}
            className={styles.debugTextarea}
            aria-label="LLM Diff Snapshot"
          />
          <button
            type="button"
            onClick={() => {
              setDebugActiveContentSnapshot('');
              setDebugLlmDiffSnapshot('');
              setDiffApplyDetailMessage('');
              setStatusMessage('');
            }}
            className={styles.button}
          >
            Clear Debug Info
          </button>
        </div>
      )}

    </div>
  );
}

const handleSaveNewFile = async (filename: string, content: string, language: string) => {
  const finalFilename = filename || `untitled.${language.toLowerCase() || 'txt'}`;
  console.log("Attempting to save new file:", finalFilename, "with content length:", content.length);

  try {
    const savedFilePath = await invoke<string>('save_new_code_file', {
      filenameSuggestion: finalFilename,
      content: content
    });
    alert(`File saved successfully at: ${savedFilePath}`);
    console.log("File saved at:", savedFilePath);
    return savedFilePath;
  } catch (error) {
    console.error("Error saving file:", error);
    alert(`Failed to save file: ${error}`);
    throw error;
  }
};
