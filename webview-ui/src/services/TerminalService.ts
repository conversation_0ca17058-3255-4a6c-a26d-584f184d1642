// Terminal service using node-pty
import { invoke } from '../lib/tauri-api';
import { Terminal } from 'xterm';
import { listen as tauriListen } from '@tauri-apps/api/event';

// Terminal event names
const TERMINAL_DATA_EVENT = 'terminal-data';
const TERMINAL_EXIT_EVENT = 'terminal-exit';

// Terminal service class
export class TerminalService {
  private term: Terminal | null = null;
  private terminalId: string | null = null;
  private dataListenerCleanup: (() => void) | null = null;
  private exitListenerCleanup: (() => void) | null = null;
  private isConnected: boolean = false;

  /**
   * Initialize the terminal service
   * @param term The xterm.js Terminal instance
   */
  public async initialize(term: Terminal): Promise<void> {
    this.term = term;

    // Set up event listeners
    await this.setupEventListeners();

    // Create a new terminal process
    await this.createTerminal();
  }

  /**
   * Set up event listeners for terminal data and exit events
   */
  private async setupEventListeners(): Promise<void> {
    if (!this.term) {
      throw new Error('Terminal not initialized');
    }

    console.log('Setting up terminal event listeners...');

    // Listen for data from the backend terminal
    console.log('Setting up terminal-data event listener...');
    this.dataListenerCleanup = await tauriListen(TERMINAL_DATA_EVENT, (event) => {
      console.log('Received terminal-data event:', event);
      const { terminalId, data } = event.payload as { terminalId: string; data: string };

      // Only process events for our terminal
      if (this.terminalId && terminalId === this.terminalId && this.term) {
        console.log(`Received data from terminal ${terminalId}:`, data);
        this.term.write(data);
      } else {
        console.log(`Ignoring data for terminal ${terminalId} (our ID: ${this.terminalId})`);
      }
    });
    console.log('Terminal-data event listener set up successfully');

    // Listen for terminal exit events
    console.log('Setting up terminal-exit event listener...');
    this.exitListenerCleanup = await tauriListen(TERMINAL_EXIT_EVENT, (event) => {
      console.log('Received terminal-exit event:', event);
      const { terminalId, exitCode } = event.payload as { terminalId: string; exitCode: number };

      // Only process events for our terminal
      if (this.terminalId && terminalId === this.terminalId && this.term) {
        console.log(`Terminal ${terminalId} exited with code ${exitCode}`);
        this.isConnected = false;
        this.term.write(`\r\nProcess exited with code ${exitCode}\r\n`);
      } else {
        console.log(`Ignoring exit for terminal ${terminalId} (our ID: ${this.terminalId})`);
      }
    });
    console.log('Terminal-exit event listener set up successfully');

    // Set up data handler for user input
    console.log('Setting up terminal onData handler...');
    this.term.onData((data) => {
      console.log('Terminal onData event:', JSON.stringify(data));

      if (this.terminalId && this.isConnected) {
        console.log(`Sending data to terminal ${this.terminalId}:`, JSON.stringify(data));
        this.writeToTerminal(data).catch(error => {
          console.error('Error writing to terminal:', error);
          if (this.term) {
            this.term.write(`\r\nError: ${error}\r\n`);
          }
        });
      } else {
        console.log(`Not sending data: terminalId=${this.terminalId}, isConnected=${this.isConnected}`);
        if (this.term) {
          this.term.write('\r\nTerminal not connected. Please wait...\r\n');
        }
      }
    });
    console.log('Terminal onData handler set up successfully');

    // Write a test message to the terminal
    if (this.term) {
      this.term.write('\r\nEvent listeners initialized. Terminal should be ready for input.\r\n');
    }
  }

  /**
   * Create a new terminal process
   */
  private async createTerminal(): Promise<void> {
    try {
      console.log('Starting terminal creation process...');

      // Set up a listener for the terminal-created event
      console.log('Setting up terminal-created event listener...');
      const createdListener = await tauriListen('terminal-created', (event) => {
        console.log('Received terminal-created event:', event);
        const { terminalId } = event.payload as { terminalId: string };
        this.terminalId = terminalId;
        this.isConnected = true;

        console.log(`Terminal created with ID: ${terminalId}`);

        if (this.term) {
          this.term.write(`Terminal connected (ID: ${terminalId})...\r\n`);

          // Force focus on the terminal
          setTimeout(() => {
            if (this.term) {
              this.term.focus();
              console.log('Terminal focused after connection');
            }
          }, 100);
        }

        // Clean up this listener as it's no longer needed
        console.log('Cleaning up terminal-created event listener');
        createdListener();
      });

      // Set up a listener for terminal errors
      console.log('Setting up terminal-error event listener...');
      const errorListener = await tauriListen('terminal-error', (event) => {
        console.log('Received terminal-error event:', event);
        const { error } = event.payload as { error: string };
        console.error('Terminal error:', error);

        if (this.term) {
          this.term.write(`\r\nTerminal error: ${error}\r\n`);
        }
      });

      // Create a new terminal process on the backend
      console.log('Invoking create_node_terminal command...');
      await invoke('create_node_terminal');
      console.log('create_node_terminal command completed');

      // Set a temporary ID until we get the real one
      this.terminalId = 'pending';
      console.log('Set temporary terminal ID to "pending"');

      if (this.term) {
        this.term.write('Initializing terminal...\r\n');
        console.log('Wrote initialization message to terminal');
      }

      // Add a timeout to check if we received the terminal ID
      setTimeout(() => {
        if (this.terminalId === 'pending') {
          console.warn('Terminal ID is still pending after 5 seconds');
          if (this.term) {
            this.term.write('\r\nWarning: Terminal connection is taking longer than expected...\r\n');
          }
        }
      }, 5000);
    } catch (error) {
      console.error('Error creating terminal:', error);
      if (this.term) {
        this.term.write(`\r\nError creating terminal: ${error}\r\n`);
      }
    }
  }

  /**
   * Write data to the terminal
   * @param data The data to write
   */
  public async writeToTerminal(data: string): Promise<void> {
    if (!this.terminalId || !this.isConnected) {
      throw new Error('Terminal not connected');
    }

    // Don't send data if the terminal ID is still pending
    if (this.terminalId === 'pending') {
      console.log('Terminal ID is still pending, buffering input');
      return;
    }

    await invoke('write_to_node_terminal', {
      terminalId: this.terminalId,
      data
    });
  }

  /**
   * Resize the terminal
   * @param cols Number of columns
   * @param rows Number of rows
   */
  public async resizeTerminal(cols: number, rows: number): Promise<void> {
    if (!this.terminalId || !this.isConnected) {
      return;
    }

    // Don't resize if the terminal ID is still pending
    if (this.terminalId === 'pending') {
      console.log('Terminal ID is still pending, skipping resize');
      return;
    }

    await invoke('resize_node_terminal', {
      terminalId: this.terminalId,
      cols,
      rows
    });
  }

  /**
   * Clean up resources
   */
  public async dispose(): Promise<void> {
    // Clean up event listeners
    if (this.dataListenerCleanup) {
      this.dataListenerCleanup();
      this.dataListenerCleanup = null;
    }

    if (this.exitListenerCleanup) {
      this.exitListenerCleanup();
      this.exitListenerCleanup = null;
    }

    // Kill the terminal process if it exists
    if (this.terminalId && this.isConnected && this.terminalId !== 'pending') {
      try {
        await invoke('kill_node_terminal', {
          terminalId: this.terminalId
        });
      } catch (error) {
        console.error('Error killing terminal:', error);
      }
    }

    this.terminalId = null;
    this.isConnected = false;
  }
}

// Export a singleton instance
export const terminalService = new TerminalService();
