{"name": "lugari-electron-shell", "version": "0.1.0", "description": "Electron shell for Lugari AI Assistant", "main": "dist/electron.js", "scripts": {"start": "electron .", "build": "tsc", "watch": "tsc -w", "package": "npm run build && electron-builder"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@types/node": "^18.0.0", "electron": "^28.0.0", "electron-builder": "^24.0.0", "typescript": "^5.0.0"}, "dependencies": {"electron-is-dev": "^3.0.0"}, "build": {"appId": "com.lugari.electronapp", "productName": "Lugari AI Assistant (Electron)", "files": ["dist/**/*", "node_modules/**/*"], "directories": {"buildResources": "assets", "output": "release"}, "mac": {"target": "dmg"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}}